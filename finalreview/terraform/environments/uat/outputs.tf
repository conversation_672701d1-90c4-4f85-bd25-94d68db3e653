# Outputs for InfoVault DEV Environment
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

# S3 Bucket Outputs
output "s3_bucket_name" {
  description = "Name of the InfoVault application data S3 bucket"
  value       = module.app_data_bucket.bucket_name
}

output "s3_bucket_arn" {
  description = "ARN of the InfoVault application data S3 bucket"
  value       = module.app_data_bucket.bucket_arn
}

output "s3_bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = module.app_data_bucket.bucket_domain_name
}

output "s3_bucket_regional_domain_name" {
  description = "Regional domain name of the S3 bucket"
  value       = module.app_data_bucket.bucket_regional_domain_name
}

output "s3_bucket_region" {
  description = "Region where the S3 bucket is created"
  value       = module.app_data_bucket.bucket_region
}

output "s3_bucket_hosted_zone_id" {
  description = "Route 53 Hosted Zone ID for the S3 bucket"
  value       = module.app_data_bucket.bucket_hosted_zone_id
}

# Environment Information
output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "aws_region" {
  description = "AWS region where resources are deployed"
  value       = var.aws_region
}

# Resource Summary
output "infrastructure_summary" {
  description = "Summary of deployed infrastructure"
  value = {
    environment       = var.environment
    region            = var.aws_region
    s3_bucket         = module.app_data_bucket.bucket_name
    s3_versioning     = module.app_data_bucket.versioning_enabled
    s3_encryption     = module.app_data_bucket.encryption_algorithm
    s3_lifecycle      = module.app_data_bucket.lifecycle_enabled
    s3_public_blocked = module.app_data_bucket.public_access_blocked
    deployed_at       = timestamp()
  }
}
# EC2 Instance Outputs

output "infovault_dev_gitlab_runner_instance_id" {
  description = "ID of the infovault-dev-gitlab-runner instance"
  value       = module.ec2_stack.infovault_dev_gitlab_runner_instance_id
}

output "infovault_dev_gitlab_runner_private_ip" {
  description = "Private IP of the infovault-dev-gitlab-runner instance"
  value       = module.ec2_stack.infovault_dev_gitlab_runner_private_ip
}

output "infovault_dev_linux_tooling_server_instance_id" {
  description = "ID of the infovault-dev-linux-tooling-server instance"
  value       = module.ec2_stack.infovault_dev_linux_tooling_server_instance_id
}

output "infovault_dev_linux_tooling_server_private_ip" {
  description = "Private IP of the infovault-dev-linux-tooling-server instance"
  value       = module.ec2_stack.infovault_dev_linux_tooling_server_private_ip
}

output "mgmt_newgenadm_win_tooling_01_instance_id" {
  description = "ID of the mgmt-newgenadm-win-tooling-01 instance"
  value       = module.ec2_stack.mgmt_newgenadm_win_tooling_01_instance_id
}

output "mgmt_newgenadm_win_tooling_01_private_ip" {
  description = "Private IP of the mgmt-newgenadm-win-tooling-01 instance"
  value       = module.ec2_stack.mgmt_newgenadm_win_tooling_01_private_ip
}

output "dev_management_server_avm150_new_instance_id" {
  description = "ID of the dev-management-server-avm150-new instance"
  value       = module.ec2_stack.dev_management_server_avm150_new_instance_id
}

output "dev_management_server_avm150_new_private_ip" {
  description = "Private IP of the dev-management-server-avm150-new instance"
  value       = module.ec2_stack.dev_management_server_avm150_new_private_ip
}

output "dev_dra_admin_server_new_instance_id" {
  description = "ID of the dev-dra-admin-server-new instance"
  value       = module.ec2_stack.dev_dra_admin_server_new_instance_id
}

output "dev_dra_admin_server_new_private_ip" {
  description = "Private IP of the dev-dra-admin-server-new instance"
  value       = module.ec2_stack.dev_dra_admin_server_new_private_ip
}

output "dev_dra_analytics_server_new_instance_id" {
  description = "ID of the dev-dra-analytics-server-new instance"
  value       = module.ec2_stack.dev_dra_analytics_server_new_instance_id
}

output "dev_dra_analytics_server_new_private_ip" {
  description = "Private IP of the dev-dra-analytics-server-new instance"
  value       = module.ec2_stack.dev_dra_analytics_server_new_private_ip
}

output "dev_management_server_avm150_imperva_instance_id" {
  description = "ID of the dev-management-server-avm150-imperva instance"
  value       = module.ec2_stack.dev_management_server_avm150_imperva_instance_id
}

output "dev_management_server_avm150_imperva_private_ip" {
  description = "Private IP of the dev-management-server-avm150-imperva instance"
  value       = module.ec2_stack.dev_management_server_avm150_imperva_private_ip
}

output "ad_tooling_windows_02_instance_id" {
  description = "ID of the ad-tooling-windows-02 instance"
  value       = module.ec2_stack.ad_tooling_windows_02_instance_id
}

output "ad_tooling_windows_02_private_ip" {
  description = "Private IP of the ad-tooling-windows-02 instance"
  value       = module.ec2_stack.ad_tooling_windows_02_private_ip
}

# =============================================================================
# INTERNET FACING COMPUTE OUTPUTS
# =============================================================================

# Squid Proxy Outputs
output "squid_proxy_instance_id" {
  description = "ID of the Squid Proxy instance"
  value       = module.internet_facing_compute.squid_proxy.instance_id
}

output "squid_proxy_private_ip" {
  description = "Private IP of the Squid Proxy instance"
  value       = module.internet_facing_compute.squid_proxy.private_ip
}

output "squid_proxy_public_ip" {
  description = "Public IP of the Squid Proxy instance"
  value       = module.internet_facing_compute.squid_proxy.public_ip
}

# Bastion Host Outputs
output "bastion_host_instance_id" {
  description = "ID of the Bastion Host instance"
  value       = module.internet_facing_compute.bastion_host.instance_id
}

output "bastion_host_private_ip" {
  description = "Private IP of the Bastion Host instance"
  value       = module.internet_facing_compute.bastion_host.private_ip
}

output "bastion_host_public_ip" {
  description = "Public IP of the Bastion Host instance"
  value       = module.internet_facing_compute.bastion_host.public_ip
}

# Internet Facing Compute Summary
output "internet_facing_compute_summary" {
  description = "Summary of internet-facing compute resources"
  value = {
    squid_proxy = {
      instance_id = module.internet_facing_compute.squid_proxy.instance_id
      private_ip  = module.internet_facing_compute.squid_proxy.private_ip
      public_ip   = module.internet_facing_compute.squid_proxy.public_ip
    }
    bastion_host = {
      instance_id = module.internet_facing_compute.bastion_host.instance_id
      private_ip  = module.internet_facing_compute.bastion_host.private_ip
      public_ip   = module.internet_facing_compute.bastion_host.public_ip
    }
  }
}

