variable "smtp_relay_config" {
  description = "Configuration for SMTP Relay instance"
  type = object({
    instance_name            = string
    ami_id                   = string
    instance_type            = string
    subnet_id                = string
    security_group_ids       = list(string)
    private_ip_address       = string
    key_name                 = string
    iam_instance_profile_name = string
    monitoring_enabled       = bool
    ebs_optimized           = bool
    source_dest_check       = bool
    user_data_script        = string
    root_block_device       = object({
      volume_type           = string
      volume_size           = number
      delete_on_termination = bool
      encrypted             = bool
    })
    ebs_block_devices       = list(object({
      device_name           = string
      volume_type           = string
      volume_size           = number
      delete_on_termination = bool
      encrypted             = bool
    }))
  })
}

variable "common_tags" {
  description = "Common tags for all resources"
  type = map(string)
  default = {}
}

# Variables for InfoVault DEV Environment
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

variable "environment" {
  description = "Environment name"
  type        = string
  default     = "dev"
}

variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "ap-southeast-1"
}

variable "project_name" {
  description = "Project name for resource naming"
  type        = string
  default     = "infovault"
}

# S3 Configuration
variable "s3_bucket_name" {
  description = "Name for the InfoVault application S3 bucket (auto-generated if empty)"
  type        = string
  default     = ""
}

variable "s3_force_destroy" {
  description = "Allow force destroy of S3 bucket (for dev/testing environments)"
  type        = bool
  default     = true
}

# S3 Versioning
variable "enable_s3_versioning" {
  description = "Enable versioning on S3 bucket"
  type        = bool
  default     = true
}

# S3 Encryption
variable "s3_encryption_algorithm" {
  description = "Server-side encryption algorithm (AES256 or aws:kms)"
  type        = string
  default     = "AES256"
}

variable "s3_kms_key_id" {
  description = "KMS key ID for encryption (required if encryption_algorithm is aws:kms)"
  type        = string
  default     = null
}

variable "s3_bucket_key_enabled" {
  description = "Enable S3 bucket key for KMS encryption"
  type        = bool
  default     = true
}

# S3 Public Access Block
variable "s3_block_public_acls" {
  description = "Block public ACLs"
  type        = bool
  default     = true
}

variable "s3_block_public_policy" {
  description = "Block public bucket policies"
  type        = bool
  default     = true
}

variable "s3_ignore_public_acls" {
  description = "Ignore public ACLs"
  type        = bool
  default     = true
}

variable "s3_restrict_public_buckets" {
  description = "Restrict public bucket policies"
  type        = bool
  default     = true
}

# S3 Lifecycle Management
variable "s3_lifecycle_enabled" {
  description = "Enable lifecycle management"
  type        = bool
  default     = true
}

variable "s3_transition_to_ia_days" {
  description = "Number of days after which to transition objects to Standard-IA"
  type        = number
  default     = 30
}

variable "s3_transition_to_glacier_days" {
  description = "Number of days after which to transition objects to Glacier"
  type        = number
  default     = 90
}

variable "s3_expiration_days" {
  description = "Number of days after which to expire objects (0 = disabled)"
  type        = number
  default     = 365
}

variable "s3_multipart_upload_days" {
  description = "Number of days after which to abort incomplete multipart uploads"
  type        = number
  default     = 7
}

# S3 Logging
variable "s3_logging_enabled" {
  description = "Enable S3 access logging"
  type        = bool
  default     = false
}

variable "s3_logging_target_bucket" {
  description = "Target bucket for access logs"
  type        = string
  default     = ""
}

variable "s3_logging_target_prefix" {
  description = "Prefix for access log objects"
  type        = string
  default     = "access-logs/"
}

# S3 Notifications
variable "s3_notification_enabled" {
  description = "Enable S3 bucket notifications"
  type        = bool
  default     = false
}

# =============================================================================
# NETWORKING VARIABLES
# =============================================================================

# VPC Compartment Configuration Objects
variable "intranet_management" {
  description = "Configuration for Intranet Management VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

variable "internet_facing" {
  description = "Configuration for Internet Facing VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

variable "gen_facing" {
  description = "Configuration for Gen Facing VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

variable "ablrhf" {
  description = "Configuration for ABLRHF VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

variable "patching" {
  description = "Configuration for Patching VPC compartment"
  type = object({
    # VPC Configuration
    vpc_cidr                             = string
    vpc_name                             = string
    enable_dns_hostnames                 = bool
    enable_dns_support                   = bool
    enable_network_address_usage_metrics = bool
    instance_tenancy                     = string
    create_internet_gateway              = bool

    # Subnets
    public_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    private_subnets = map(object({
      cidr_block                      = string
      availability_zone               = string
      map_public_ip_on_launch         = bool
      assign_ipv6_address_on_creation = bool
      name                            = string
    }))

    # NAT Gateways
    nat_gateways = map(object({
      name       = string
      subnet_key = string
    }))

    # Route Tables
    public_route_tables = map(object({
      name = string
    }))

    private_route_tables = map(object({
      name = string
    }))

    # Route Table Associations
    public_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    private_subnet_route_associations = map(object({
      subnet_key      = string
      route_table_key = string
    }))

    # Routes
    private_nat_routes = map(object({
      route_table_key        = string
      destination_cidr_block = string
      nat_gateway_key        = string
    }))

    vpc_peering_routes = map(object({
      route_table_key           = string
      route_table_type          = string
      destination_cidr_block    = string
      vpc_peering_connection_id = string
    }))

    # Security Groups
    security_groups = map(object({
      name_prefix = string
      description = string
      name        = string
    }))

    security_group_ingress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    security_group_egress_rules = map(object({
      security_group_key       = string
      from_port                = number
      to_port                  = number
      protocol                 = string
      cidr_blocks              = list(string)
      source_security_group_id = string
      description              = string
    }))

    # Network ACLs
    network_acls = map(object({
      name        = string
      subnet_keys = list(string)
    }))

    network_acl_ingress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    network_acl_egress_rules = map(object({
      network_acl_key = string
      rule_number     = number
      protocol        = string
      rule_action     = string
      cidr_block      = string
      from_port       = number
      to_port         = number
    }))

    # VPC Endpoints
    vpc_endpoints = map(object({
      name                = string
      service_name        = string
      vpc_endpoint_type   = string
      route_table_ids     = list(string)
      subnet_ids          = list(string)
      security_group_ids  = list(string)
      private_dns_enabled = bool
    }))
  })
}

# Common Tags (duplicate removed - using the one at line 32)

# =============================================================================
# KUBERNETES INFRASTRUCTURE VARIABLES
# =============================================================================

# Storage Configuration
variable "create_shared_storage" {
  description = "Create shared PVC for applications"
  type        = bool
  default     = false
}

variable "shared_storage_size" {
  description = "Size of shared storage PVC"
  type        = string
  default     = "10Gi"
}

# Infrastructure Configuration
variable "infrastructure_config" {
  description = "Infrastructure-level configuration"
  type        = map(string)
  default     = {}
}

# =============================================================================
# PATCHING WORKLOADS VARIABLES
# =============================================================================

# ============================================
# WSUS Server Variables
# ============================================

variable "wsus_instance_name" {
  description = "Name for the WSUS EC2 instance"
  type        = string
}

variable "wsus_ami_id" {
  description = "Windows Server AMI ID for WSUS server"
  type        = string
}

variable "wsus_instance_type" {
  description = "EC2 instance type for WSUS server"
  type        = string
}

variable "wsus_subnet_id" {
  description = "Subnet ID for WSUS server deployment"
  type        = string
}

variable "wsus_security_group_ids" {
  description = "List of security group IDs for WSUS server"
  type        = list(string)
}

variable "wsus_private_ip" {
  description = "Static private IP for WSUS server"
  type        = string
  default     = null
}

variable "wsus_iam_instance_profile" {
  description = "IAM instance profile name for WSUS server"
  type        = string
}

# WSUS Configuration Variables
variable "wsus_port" {
  description = "HTTP port for WSUS server"
  type        = number
  default     = 8530
}

variable "wsus_ssl_port" {
  description = "HTTPS port for WSUS server"
  type        = number
  default     = 8531
}

variable "wsus_content_dir" {
  description = "Directory for WSUS content storage"
  type        = string
  default     = "C:\\WSUS"
}

variable "wsus_database_name" {
  description = "WSUS database name"
  type        = string
  default     = "SUSDB"
}

variable "wsus_update_languages" {
  description = "List of update languages for WSUS"
  type        = list(string)
  default     = ["en"]
}

variable "wsus_update_classifications" {
  description = "List of update classifications to sync"
  type        = list(string)
  default     = [
    "Critical Updates",
    "Security Updates",
    "Updates",
    "Update Rollups",
    "Service Packs"
  ]
}

variable "wsus_products" {
  description = "List of products to sync updates for"
  type        = list(string)
  default     = [
    "Windows Server 2019",
    "Windows Server 2022",
    "Windows 10",
    "Windows 11"
  ]
}

# WSUS Admin Configuration
variable "wsus_admin_users" {
  description = "List of admin users to create on WSUS server"
  type        = list(string)
  default     = []
}

variable "wsus_admin_passwords" {
  description = "Map of admin user passwords"
  type        = map(string)
  default     = {}
  sensitive   = true
}

variable "wsus_domain_join_enabled" {
  description = "Whether to join WSUS server to domain"
  type        = bool
  default     = false
}

variable "wsus_domain_name" {
  description = "Domain name to join WSUS server to"
  type        = string
  default     = ""
}

variable "wsus_domain_user" {
  description = "Domain user for joining WSUS server"
  type        = string
  default     = ""
}

variable "wsus_domain_password" {
  description = "Domain password for joining WSUS server"
  type        = string
  default     = ""
  sensitive   = true
}

# WSUS Monitoring
variable "wsus_cloudwatch_log_group" {
  description = "CloudWatch log group for WSUS server"
  type        = string
  default     = ""
}

variable "wsus_enable_monitoring" {
  description = "Enable detailed monitoring for WSUS server"
  type        = bool
  default     = false
}

# ============================================
# RHEL Repository Server Variables
# ============================================

variable "rhel_instance_name" {
  description = "Name for the RHEL repository EC2 instance"
  type        = string
}

variable "rhel_ami_id" {
  description = "Amazon Linux 2 AMI ID for RHEL repository server"
  type        = string
}

variable "rhel_instance_type" {
  description = "EC2 instance type for RHEL repository server"
  type        = string
}

variable "rhel_subnet_id" {
  description = "Subnet ID for RHEL repository server deployment"
  type        = string
}

variable "rhel_security_group_ids" {
  description = "List of security group IDs for RHEL repository server"
  type        = list(string)
}

variable "rhel_private_ip" {
  description = "Static private IP for RHEL repository server"
  type        = string
  default     = null
}

variable "rhel_iam_instance_profile" {
  description = "IAM instance profile name for RHEL repository server"
  type        = string
}

# Repository Configuration Variables
variable "rhel_repository_port" {
  description = "HTTP port for repository server"
  type        = number
  default     = 80
}

variable "rhel_repository_root" {
  description = "Repository root directory"
  type        = string
  default     = "/var/www/html/repos"
}

variable "rhel_repository_distributions" {
  description = "List of supported distributions"
  type        = list(string)
  default     = [
    "rhel8",
    "rhel9",
    "centos7",
    "centos8"
  ]
}

variable "rhel_repository_architectures" {
  description = "List of supported architectures"
  type        = list(string)
  default     = ["x86_64", "noarch"]
}

# Upstream Repository Sync
variable "rhel_upstream_repositories" {
  description = "List of upstream repositories to sync"
  type = list(object({
    name         = string
    url          = string
    distribution = string
    architecture = string
  }))
  default = []
}

variable "rhel_sync_schedule" {
  description = "Cron schedule for upstream repository synchronization"
  type        = string
  default     = "0 3 * * *"  # Daily at 3 AM
}

# RHEL Admin Configuration
variable "rhel_admin_users" {
  description = "List of admin users to create on RHEL repository server"
  type        = list(string)
  default     = []
}

variable "rhel_ssh_public_keys" {
  description = "List of SSH public keys for admin access"
  type        = list(string)
  default     = []
}

# RHEL Monitoring
variable "rhel_cloudwatch_log_group" {
  description = "CloudWatch log group for RHEL repository server"
  type        = string
  default     = ""
}

variable "rhel_enable_monitoring" {
  description = "Enable detailed monitoring for RHEL repository server"
  type        = bool
  default     = false
}
# =============================================================================
# EKS CONFIGURATION VARIABLES
# =============================================================================

# EKS Cluster Configuration
variable "cluster_name" {
  description = "Name of the EKS cluster"
  type        = string
}

variable "cluster_version" {
  description = "Kubernetes version for the EKS cluster"
  type        = string
}

variable "cluster_role_name" {
  description = "Name of the IAM role for the EKS cluster"
  type        = string
}

variable "node_group_role_name" {
  description = "Name of the IAM role for EKS node groups"
  type        = string
}

variable "subnet_ids" {
  description = "List of subnet IDs for the EKS cluster"
  type        = list(string)
}

variable "cluster_security_group_ids" {
  description = "List of security group IDs for the EKS cluster"
  type        = list(string)
}

variable "endpoint_private_access" {
  description = "Enable private API server endpoint"
  type        = bool
}

variable "endpoint_public_access" {
  description = "Enable public API server endpoint"
  type        = bool
}

variable "public_access_cidrs" {
  description = "List of CIDR blocks for public access"
  type        = list(string)
}

variable "cluster_log_types" {
  description = "List of cluster log types to enable"
  type        = list(string)
}

variable "kms_key_arn" {
  description = "ARN of KMS key for cluster encryption"
  type        = string
}

# Node Group Variables
variable "node_group_name" {
  description = "Name of the EKS node group"
  type        = string
}

variable "node_group_subnet_ids" {
  description = "Subnet IDs for the node group"
  type        = list(string)
}

variable "node_group_instance_types" {
  description = "Instance types for the node group"
  type        = list(string)
}

variable "node_group_ami_type" {
  description = "AMI type for the node group"
  type        = string
}

variable "node_group_capacity_type" {
  description = "Capacity type for the node group"
  type        = string
}

variable "node_group_disk_size" {
  description = "Disk size for the node group"
  type        = number
}

variable "node_group_desired_size" {
  description = "Desired size for the node group"
  type        = number
}

variable "node_group_max_size" {
  description = "Maximum size for the node group"
  type        = number
}

variable "node_group_min_size" {
  description = "Minimum size for the node group"
  type        = number
}

variable "node_group_labels" {
  description = "Labels for the node group"
  type        = map(string)
}

# EKS Addon Version Variables
variable "addon_amazon_cloudwatch_observability_version" {
  description = "Version for amazon-cloudwatch-observability addon"
  type        = string
}

variable "addon_aws_ebs_csi_driver_version" {
  description = "Version for aws-ebs-csi-driver addon"
  type        = string
}

variable "addon_aws_guardduty_agent_version" {
  description = "Version for aws-guardduty-agent addon"
  type        = string
}

variable "addon_coredns_version" {
  description = "Version for coredns addon"
  type        = string
}

variable "addon_eks_pod_identity_agent_version" {
  description = "Version for eks-pod-identity-agent addon"
  type        = string
}

variable "addon_kube_proxy_version" {
  description = "Version for kube-proxy addon"
  type        = string
}

variable "addon_vpc_cni_version" {
  description = "Version for vpc-cni addon"
  type        = string
}
# Variables for infovault-dev-gitlab-runner
variable "infovault_dev_gitlab_runner_name" {
  description = "Name of the infovault-dev-gitlab-runner instance"
  type        = string
}

variable "infovault_dev_gitlab_runner_ami_id" {
  description = "AMI ID for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_instance_type" {
  description = "Instance type for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_key_name" {
  description = "Key pair name for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_security_group_ids" {
  description = "Security group IDs for infovault-dev-gitlab-runner"
  type        = list(string)
}

variable "infovault_dev_gitlab_runner_subnet_id" {
  description = "Subnet ID for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_iam_instance_profile_name" {
  description = "IAM instance profile name for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_availability_zone" {
  description = "Availability zone for infovault-dev-gitlab-runner"
  type        = string
}

variable "infovault_dev_gitlab_runner_monitoring_enabled" {
  description = "Enable monitoring for infovault-dev-gitlab-runner"
  type        = bool
  default     = false
}

variable "infovault_dev_gitlab_runner_ebs_optimized" {
  description = "Enable EBS optimization for infovault-dev-gitlab-runner"
  type        = bool
  default     = false
}

variable "infovault_dev_gitlab_runner_source_dest_check" {
  description = "Enable source destination check for infovault-dev-gitlab-runner"
  type        = bool
  default     = true
}

variable "infovault_dev_gitlab_runner_private_ip_address" {
  description = "Private IP address for infovault-dev-gitlab-runner"
  type        = string
  default     = null
}

variable "infovault_dev_gitlab_runner_root_block_device" {
  description = "Root block device configuration for infovault-dev-gitlab-runner"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "infovault_dev_gitlab_runner_ebs_block_devices" {
  description = "Additional EBS block devices for infovault-dev-gitlab-runner"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

# Variables for infovault-dev-linux-tooling-server
variable "infovault_dev_linux_tooling_server_name" {
  description = "Name of the infovault-dev-linux-tooling-server instance"
  type        = string
}

variable "infovault_dev_linux_tooling_server_ami_id" {
  description = "AMI ID for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_instance_type" {
  description = "Instance type for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_key_name" {
  description = "Key pair name for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_security_group_ids" {
  description = "Security group IDs for infovault-dev-linux-tooling-server"
  type        = list(string)
}

variable "infovault_dev_linux_tooling_server_subnet_id" {
  description = "Subnet ID for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_iam_instance_profile_name" {
  description = "IAM instance profile name for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_availability_zone" {
  description = "Availability zone for infovault-dev-linux-tooling-server"
  type        = string
}

variable "infovault_dev_linux_tooling_server_monitoring_enabled" {
  description = "Enable monitoring for infovault-dev-linux-tooling-server"
  type        = bool
  default     = false
}

variable "infovault_dev_linux_tooling_server_ebs_optimized" {
  description = "Enable EBS optimization for infovault-dev-linux-tooling-server"
  type        = bool
  default     = false
}

variable "infovault_dev_linux_tooling_server_source_dest_check" {
  description = "Enable source destination check for infovault-dev-linux-tooling-server"
  type        = bool
  default     = true
}

variable "infovault_dev_linux_tooling_server_private_ip_address" {
  description = "Private IP address for infovault-dev-linux-tooling-server"
  type        = string
  default     = null
}

variable "infovault_dev_linux_tooling_server_root_block_device" {
  description = "Root block device configuration for infovault-dev-linux-tooling-server"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "infovault_dev_linux_tooling_server_ebs_block_devices" {
  description = "Additional EBS block devices for infovault-dev-linux-tooling-server"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

# Variables for mgmt-newgenadm-win-tooling-01
variable "mgmt_newgenadm_win_tooling_01_name" {
  description = "Name of the mgmt-newgenadm-win-tooling-01 instance"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_ami_id" {
  description = "AMI ID for mgmt-newgenadm-win-tooling-01"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_instance_type" {
  description = "Instance type for mgmt-newgenadm-win-tooling-01"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_key_name" {
  description = "Key pair name for mgmt-newgenadm-win-tooling-01"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_security_group_ids" {
  description = "Security group IDs for mgmt-newgenadm-win-tooling-01"
  type        = list(string)
}

variable "mgmt_newgenadm_win_tooling_01_subnet_id" {
  description = "Subnet ID for mgmt-newgenadm-win-tooling-01"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_iam_instance_profile_name" {
  description = "IAM instance profile name for mgmt-newgenadm-win-tooling-01"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_availability_zone" {
  description = "Availability zone for mgmt-newgenadm-win-tooling-01"
  type        = string
}

variable "mgmt_newgenadm_win_tooling_01_monitoring_enabled" {
  description = "Enable monitoring for mgmt-newgenadm-win-tooling-01"
  type        = bool
  default     = false
}

variable "mgmt_newgenadm_win_tooling_01_ebs_optimized" {
  description = "Enable EBS optimization for mgmt-newgenadm-win-tooling-01"
  type        = bool
  default     = false
}

variable "mgmt_newgenadm_win_tooling_01_source_dest_check" {
  description = "Enable source destination check for mgmt-newgenadm-win-tooling-01"
  type        = bool
  default     = true
}

variable "mgmt_newgenadm_win_tooling_01_private_ip_address" {
  description = "Private IP address for mgmt-newgenadm-win-tooling-01"
  type        = string
  default     = null
}

variable "mgmt_newgenadm_win_tooling_01_root_block_device" {
  description = "Root block device configuration for mgmt-newgenadm-win-tooling-01"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "mgmt_newgenadm_win_tooling_01_ebs_block_devices" {
  description = "Additional EBS block devices for mgmt-newgenadm-win-tooling-01"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

# Variables for dev-management-server-avm150-new
variable "dev_management_server_avm150_new_name" {
  description = "Name of the dev-management-server-avm150-new instance"
  type        = string
}

variable "dev_management_server_avm150_new_ami_id" {
  description = "AMI ID for dev-management-server-avm150-new"
  type        = string
}

variable "dev_management_server_avm150_new_instance_type" {
  description = "Instance type for dev-management-server-avm150-new"
  type        = string
}

variable "dev_management_server_avm150_new_key_name" {
  description = "Key pair name for dev-management-server-avm150-new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_security_group_ids" {
  description = "Security group IDs for dev-management-server-avm150-new"
  type        = list(string)
}

variable "dev_management_server_avm150_new_subnet_id" {
  description = "Subnet ID for dev-management-server-avm150-new"
  type        = string
}

variable "dev_management_server_avm150_new_iam_instance_profile_name" {
  description = "IAM instance profile name for dev-management-server-avm150-new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_availability_zone" {
  description = "Availability zone for dev-management-server-avm150-new"
  type        = string
}

variable "dev_management_server_avm150_new_monitoring_enabled" {
  description = "Enable monitoring for dev-management-server-avm150-new"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_new_ebs_optimized" {
  description = "Enable EBS optimization for dev-management-server-avm150-new"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_new_source_dest_check" {
  description = "Enable source destination check for dev-management-server-avm150-new"
  type        = bool
  default     = true
}

variable "dev_management_server_avm150_new_private_ip_address" {
  description = "Private IP address for dev-management-server-avm150-new"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_new_root_block_device" {
  description = "Root block device configuration for dev-management-server-avm150-new"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_management_server_avm150_new_ebs_block_devices" {
  description = "Additional EBS block devices for dev-management-server-avm150-new"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

# Variables for dev-dra-admin-server-new
variable "dev_dra_admin_server_new_name" {
  description = "Name of the dev-dra-admin-server-new instance"
  type        = string
}

variable "dev_dra_admin_server_new_ami_id" {
  description = "AMI ID for dev-dra-admin-server-new"
  type        = string
}

variable "dev_dra_admin_server_new_instance_type" {
  description = "Instance type for dev-dra-admin-server-new"
  type        = string
}

variable "dev_dra_admin_server_new_key_name" {
  description = "Key pair name for dev-dra-admin-server-new"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_security_group_ids" {
  description = "Security group IDs for dev-dra-admin-server-new"
  type        = list(string)
}

variable "dev_dra_admin_server_new_subnet_id" {
  description = "Subnet ID for dev-dra-admin-server-new"
  type        = string
}

variable "dev_dra_admin_server_new_iam_instance_profile_name" {
  description = "IAM instance profile name for dev-dra-admin-server-new"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_availability_zone" {
  description = "Availability zone for dev-dra-admin-server-new"
  type        = string
}

variable "dev_dra_admin_server_new_monitoring_enabled" {
  description = "Enable monitoring for dev-dra-admin-server-new"
  type        = bool
  default     = false
}

variable "dev_dra_admin_server_new_ebs_optimized" {
  description = "Enable EBS optimization for dev-dra-admin-server-new"
  type        = bool
  default     = false
}

variable "dev_dra_admin_server_new_source_dest_check" {
  description = "Enable source destination check for dev-dra-admin-server-new"
  type        = bool
  default     = true
}

variable "dev_dra_admin_server_new_private_ip_address" {
  description = "Private IP address for dev-dra-admin-server-new"
  type        = string
  default     = null
}

variable "dev_dra_admin_server_new_root_block_device" {
  description = "Root block device configuration for dev-dra-admin-server-new"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_dra_admin_server_new_ebs_block_devices" {
  description = "Additional EBS block devices for dev-dra-admin-server-new"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

# Variables for dev-dra-analytics-server-new
variable "dev_dra_analytics_server_new_name" {
  description = "Name of the dev-dra-analytics-server-new instance"
  type        = string
}

variable "dev_dra_analytics_server_new_ami_id" {
  description = "AMI ID for dev-dra-analytics-server-new"
  type        = string
}

variable "dev_dra_analytics_server_new_instance_type" {
  description = "Instance type for dev-dra-analytics-server-new"
  type        = string
}

variable "dev_dra_analytics_server_new_key_name" {
  description = "Key pair name for dev-dra-analytics-server-new"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_security_group_ids" {
  description = "Security group IDs for dev-dra-analytics-server-new"
  type        = list(string)
}

variable "dev_dra_analytics_server_new_subnet_id" {
  description = "Subnet ID for dev-dra-analytics-server-new"
  type        = string
}

variable "dev_dra_analytics_server_new_iam_instance_profile_name" {
  description = "IAM instance profile name for dev-dra-analytics-server-new"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_availability_zone" {
  description = "Availability zone for dev-dra-analytics-server-new"
  type        = string
}

variable "dev_dra_analytics_server_new_monitoring_enabled" {
  description = "Enable monitoring for dev-dra-analytics-server-new"
  type        = bool
  default     = false
}

variable "dev_dra_analytics_server_new_ebs_optimized" {
  description = "Enable EBS optimization for dev-dra-analytics-server-new"
  type        = bool
  default     = false
}

variable "dev_dra_analytics_server_new_source_dest_check" {
  description = "Enable source destination check for dev-dra-analytics-server-new"
  type        = bool
  default     = true
}

variable "dev_dra_analytics_server_new_private_ip_address" {
  description = "Private IP address for dev-dra-analytics-server-new"
  type        = string
  default     = null
}

variable "dev_dra_analytics_server_new_root_block_device" {
  description = "Root block device configuration for dev-dra-analytics-server-new"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_dra_analytics_server_new_ebs_block_devices" {
  description = "Additional EBS block devices for dev-dra-analytics-server-new"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

# Variables for dev-management-server-avm150-imperva
variable "dev_management_server_avm150_imperva_name" {
  description = "Name of the dev-management-server-avm150-imperva instance"
  type        = string
}

variable "dev_management_server_avm150_imperva_ami_id" {
  description = "AMI ID for dev-management-server-avm150-imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_instance_type" {
  description = "Instance type for dev-management-server-avm150-imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_key_name" {
  description = "Key pair name for dev-management-server-avm150-imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_security_group_ids" {
  description = "Security group IDs for dev-management-server-avm150-imperva"
  type        = list(string)
}

variable "dev_management_server_avm150_imperva_subnet_id" {
  description = "Subnet ID for dev-management-server-avm150-imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_iam_instance_profile_name" {
  description = "IAM instance profile name for dev-management-server-avm150-imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_availability_zone" {
  description = "Availability zone for dev-management-server-avm150-imperva"
  type        = string
}

variable "dev_management_server_avm150_imperva_monitoring_enabled" {
  description = "Enable monitoring for dev-management-server-avm150-imperva"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_imperva_ebs_optimized" {
  description = "Enable EBS optimization for dev-management-server-avm150-imperva"
  type        = bool
  default     = false
}

variable "dev_management_server_avm150_imperva_source_dest_check" {
  description = "Enable source destination check for dev-management-server-avm150-imperva"
  type        = bool
  default     = true
}

variable "dev_management_server_avm150_imperva_private_ip_address" {
  description = "Private IP address for dev-management-server-avm150-imperva"
  type        = string
  default     = null
}

variable "dev_management_server_avm150_imperva_root_block_device" {
  description = "Root block device configuration for dev-management-server-avm150-imperva"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dev_management_server_avm150_imperva_ebs_block_devices" {
  description = "Additional EBS block devices for dev-management-server-avm150-imperva"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

# Variables for ad-tooling-windows-02
variable "ad_tooling_windows_02_name" {
  description = "Name of the ad-tooling-windows-02 instance"
  type        = string
}

variable "ad_tooling_windows_02_ami_id" {
  description = "AMI ID for ad-tooling-windows-02"
  type        = string
}

variable "ad_tooling_windows_02_instance_type" {
  description = "Instance type for ad-tooling-windows-02"
  type        = string
}

variable "ad_tooling_windows_02_key_name" {
  description = "Key pair name for ad-tooling-windows-02"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_security_group_ids" {
  description = "Security group IDs for ad-tooling-windows-02"
  type        = list(string)
}

variable "ad_tooling_windows_02_subnet_id" {
  description = "Subnet ID for ad-tooling-windows-02"
  type        = string
}

variable "ad_tooling_windows_02_iam_instance_profile_name" {
  description = "IAM instance profile name for ad-tooling-windows-02"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_availability_zone" {
  description = "Availability zone for ad-tooling-windows-02"
  type        = string
}

variable "ad_tooling_windows_02_monitoring_enabled" {
  description = "Enable monitoring for ad-tooling-windows-02"
  type        = bool
  default     = false
}

variable "ad_tooling_windows_02_ebs_optimized" {
  description = "Enable EBS optimization for ad-tooling-windows-02"
  type        = bool
  default     = false
}

variable "ad_tooling_windows_02_source_dest_check" {
  description = "Enable source destination check for ad-tooling-windows-02"
  type        = bool
  default     = true
}

variable "ad_tooling_windows_02_private_ip_address" {
  description = "Private IP address for ad-tooling-windows-02"
  type        = string
  default     = null
}

variable "ad_tooling_windows_02_root_block_device" {
  description = "Root block device configuration for ad-tooling-windows-02"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "ad_tooling_windows_02_ebs_block_devices" {
  description = "Additional EBS block devices for ad-tooling-windows-02"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

# =============================================================================
# INTERNET FACING COMPUTE VARIABLES
# =============================================================================

# Squid Proxy Configuration
variable "squid_proxy" {
  description = "Configuration for the Squid Proxy instance"
  type = object({
    instance_name              = string
    ami_id                     = string
    instance_type              = string
    key_name                   = string
    subnet_id                  = string
    security_group_ids         = list(string)
    availability_zone          = string
    private_ip_address         = optional(string)
    monitoring_enabled         = optional(bool, false)
    ebs_optimized              = optional(bool, true)
    source_dest_check          = optional(bool, false)
    associate_public_ip_address = optional(bool, true)
    
    # IAM configuration
    create_iam_role                        = optional(bool, false)
    iam_role_name                          = optional(string)
    existing_iam_role_name                 = optional(string)
    create_iam_instance_profile            = optional(bool, false)
    iam_instance_profile_name              = optional(string)
    existing_iam_instance_profile_name     = optional(string)
    
    # Storage configuration
    root_block_device = optional(object({
      volume_type           = string
      volume_size           = number
      delete_on_termination = bool
      encrypted             = bool
    }), {
      volume_type           = "gp3"
      volume_size           = 20
      delete_on_termination = true
      encrypted             = true
    })
    
    ebs_block_devices = optional(list(object({
      device_name           = string
      volume_type           = string
      volume_size           = number
      delete_on_termination = bool
      encrypted             = bool
    })), [])
    
    # Squid configuration
    squid_config = optional(object({
      port                    = number
      cache_dir_size_mb      = number
      maximum_object_size_mb = number
      access_log_enabled     = bool
      denied_sites           = list(string)
      allowed_domains        = list(string)
      client_subnets         = list(string)
    }), {
      port                    = 3128
      cache_dir_size_mb      = 1000
      maximum_object_size_mb = 50
      access_log_enabled     = true
      denied_sites           = []
      allowed_domains        = []
      client_subnets         = ["**********/16"]
    })
  })
}

# Bastion Host Configuration
variable "bastion_host" {
  description = "Configuration for the Bastion Host instance"
  type = object({
    instance_name              = string
    ami_id                     = string
    instance_type              = string
    key_name                   = string
    subnet_id                  = string
    security_group_ids         = list(string)
    availability_zone          = string
    private_ip_address         = optional(string)
    platform                   = string
    monitoring_enabled         = optional(bool, false)
    ebs_optimized              = optional(bool, true)
    source_dest_check          = optional(bool, true)
    associate_public_ip_address = optional(bool, true)
    
    # IAM configuration
    create_iam_role                        = optional(bool, false)
    iam_role_name                          = optional(string)
    existing_iam_role_name                 = optional(string)
    create_iam_instance_profile            = optional(bool, false)
    iam_instance_profile_name              = optional(string)
    existing_iam_instance_profile_name     = optional(string)
    
    # Storage configuration
    root_block_device = optional(object({
      volume_type           = string
      volume_size           = number
      delete_on_termination = bool
      encrypted             = bool
    }), {
      volume_type           = "gp3"
      volume_size           = 30
      delete_on_termination = true
      encrypted             = true
    })
    
    ebs_block_devices = optional(list(object({
      device_name           = string
      volume_type           = string
      volume_size           = number
      delete_on_termination = bool
      encrypted             = bool
    })), [])
  })
}

# =============================================================================
# EKS VPC ENDPOINTS CONFIGURATION
# =============================================================================

variable "eks_vpc_endpoints" {
  description = "Configuration for EKS VPC endpoints"
  type = object({
    subnet_ids                = list(string)
    ssm_subnet_ids           = list(string)
    route_table_ids          = list(string)
    enable_ec2_endpoint      = optional(bool, true)
    enable_ecr_endpoints     = optional(bool, true)
    enable_eks_endpoint      = optional(bool, true)
    enable_s3_endpoint       = optional(bool, true)
    enable_ssm_endpoints     = optional(bool, true)
    enable_guardduty_endpoint = optional(bool, true)
  })
}

# =============================================================================
# IAM ROLES FOR SERVICE ACCOUNTS (IRSA) CONFIGURATION
# =============================================================================

variable "irsa" {
  description = "Configuration for IAM Roles for Service Accounts"
  type = object({
    aws_load_balancer_controller_role_name = string
    cert_manager_role_name                 = string
    cluster_autoscaler_role_name           = string
    external_dns_role_name                 = string
  })
}

# =============================================================================
# EKS NODE POLICIES CONFIGURATION
# =============================================================================

variable "eks_node_policies" {
  description = "Configuration for EKS node custom policies"
  type = object({
    s3_access_policy_name      = string
    kms_access_policy_name     = string
    attach_to_node_group_role  = optional(bool, true)
    attach_additional_policies = optional(bool, true)
  })
}

# =============================================================================
# KUBERNETES CONTROLLERS CONFIGURATION
# =============================================================================

variable "kubernetes_controllers" {
  description = "Configuration for Kubernetes controllers"
  type = object({
    enable_aws_load_balancer_controller  = optional(bool, true)
    aws_load_balancer_controller_version = optional(string, "1.8.1")
    
    enable_cert_manager                  = optional(bool, true)
    cert_manager_version                 = optional(string, "v1.13.3")
    enable_cert_manager_pca_issuer       = optional(bool, true)
    cert_manager_pca_issuer_version      = optional(string, "v1.2.6")
    
    enable_cluster_autoscaler            = optional(bool, true)
    cluster_autoscaler_version           = optional(string, "9.34.1")
    
    enable_external_dns                  = optional(bool, true)
    external_dns_version                 = optional(string, "1.14.3")
  })
}

# =============================================================================
# DATABASE INFRASTRUCTURE VARIABLES
# =============================================================================

# Database Network Configuration
variable "database_vpc_id" {
  description = "VPC ID where the database resources will be created"
  type        = string
}

variable "database_subnet_dlz1_id" {
  description = "Database subnet ID in DLZ1 (AZ1)"
  type        = string
}

variable "database_subnet_dlz2_id" {
  description = "Database subnet ID in DLZ2 (AZ2)"
  type        = string
}

variable "management_subnet_cidr" {
  description = "CIDR block of management subnet for database access"
  type        = string
}

variable "database_port" {
  description = "Port for database connections"
  type        = number
  default     = 1433
}

# Database Security Configuration
variable "create_application_security_group" {
  description = "Whether to create a security group for applications"
  type        = bool
  default     = true
}

variable "additional_database_ingress_rules" {
  description = "Additional ingress rules for database security group"
  type = map(object({
    from_port                = number
    to_port                  = number
    protocol                 = string
    description              = string
    cidr_blocks              = optional(list(string))
    ipv6_cidr_blocks         = optional(list(string))
    source_security_group_id = optional(string)
  }))
  default = {}
}

variable "database_egress_rules" {
  description = "Egress rules for database security group"
  type = map(object({
    from_port                = number
    to_port                  = number
    protocol                 = string
    description              = string
    cidr_blocks              = optional(list(string))
    ipv6_cidr_blocks         = optional(list(string))
    source_security_group_id = optional(string)
  }))
  default = {}
}

variable "application_ingress_rules" {
  description = "Ingress rules for application security group"
  type = map(object({
    from_port                = number
    to_port                  = number
    protocol                 = string
    description              = string
    cidr_blocks              = optional(list(string))
    ipv6_cidr_blocks         = optional(list(string))
    source_security_group_id = optional(string)
  }))
  default = {}
}

# Enhanced monitoring and connection configuration
variable "create_monitoring_role" {
  description = "Whether to create IAM role for enhanced monitoring"
  type        = bool
  default     = true
}

variable "create_connection_secret" {
  description = "Whether to create a secret for database connections"
  type        = bool
  default     = true
}

variable "existing_database_endpoint" {
  description = "Existing database endpoint for connection secret"
  type        = string
  default     = ""
}

variable "database_name" {
  description = "Name of the database"
  type        = string
  default     = ""
}

variable "database_username" {
  description = "Database username for connection secret"
  type        = string
  default     = "sa"
}

# Primary Database Configuration
variable "create_primary_database" {
  description = "Whether to create the primary database instance"
  type        = bool
  default     = true
}

variable "primary_db_engine" {
  description = "Database engine for primary instance"
  type        = string
  default     = "sqlserver-ex"
}

variable "primary_db_engine_version" {
  description = "Database engine version for primary instance"
  type        = string
}

variable "primary_db_instance_class" {
  description = "Instance class for primary database"
  type        = string
}

variable "primary_db_name" {
  description = "Name of the primary database"
  type        = string
  default     = null
}

variable "primary_db_master_username" {
  description = "Master username for primary database"
  type        = string
  default     = "sa"
}

variable "manage_master_user_password" {
  description = "Whether to manage master user password with Secrets Manager"
  type        = bool
  default     = true
}

variable "primary_db_allocated_storage" {
  description = "Allocated storage for primary database (GB)"
  type        = number
}

variable "primary_db_max_allocated_storage" {
  description = "Maximum allocated storage for primary database (GB)"
  type        = number
  default     = null
}

variable "primary_db_storage_type" {
  description = "Storage type for primary database"
  type        = string
  default     = "gp3"
}

variable "primary_db_storage_encrypted" {
  description = "Whether to encrypt primary database storage"
  type        = bool
  default     = true
}

variable "primary_db_kms_key_id" {
  description = "KMS key ID for primary database encryption"
  type        = string
  default     = null
}

variable "primary_db_publicly_accessible" {
  description = "Whether primary database is publicly accessible"
  type        = bool
  default     = false
}

variable "primary_db_multi_az" {
  description = "Whether to enable Multi-AZ for primary database"
  type        = bool
  default     = true
}

variable "primary_db_backup_retention_period" {
  description = "Backup retention period for primary database (days)"
  type        = number
  default     = 7
}

variable "primary_db_backup_window" {
  description = "Backup window for primary database"
  type        = string
  default     = "03:00-04:00"
}

variable "primary_db_maintenance_window" {
  description = "Maintenance window for primary database"
  type        = string
  default     = "sun:04:00-sun:05:00"
}

variable "primary_db_deletion_protection" {
  description = "Whether to enable deletion protection for primary database"
  type        = bool
  default     = true
}

variable "primary_db_skip_final_snapshot" {
  description = "Whether to skip final snapshot for primary database"
  type        = bool
  default     = false
}

# Read Replica Configuration
variable "create_read_replica" {
  description = "Whether to create a read replica database"
  type        = bool
  default     = false
}

variable "replica_db_engine" {
  description = "Database engine for read replica"
  type        = string
  default     = "sqlserver-ex"
}

variable "replica_db_engine_version" {
  description = "Database engine version for read replica"
  type        = string
  default     = ""
}

variable "replica_db_instance_class" {
  description = "Instance class for read replica"
  type        = string
  default     = ""
}

variable "replica_db_master_username" {
  description = "Master username for read replica"
  type        = string
  default     = "sa"
}

variable "replica_db_allocated_storage" {
  description = "Allocated storage for read replica (GB)"
  type        = number
  default     = 0
}

variable "replica_db_max_allocated_storage" {
  description = "Maximum allocated storage for read replica (GB)"
  type        = number
  default     = null
}

variable "replica_db_storage_type" {
  description = "Storage type for read replica"
  type        = string
  default     = "gp3"
}

variable "replica_db_storage_encrypted" {
  description = "Whether to encrypt read replica storage"
  type        = bool
  default     = true
}

variable "replica_db_publicly_accessible" {
  description = "Whether read replica is publicly accessible"
  type        = bool
  default     = false
}

variable "replica_db_multi_az" {
  description = "Whether to enable Multi-AZ for read replica"
  type        = bool
  default     = false
}

variable "replica_db_maintenance_window" {
  description = "Maintenance window for read replica"
  type        = string
  default     = "sun:05:00-sun:06:00"
}

variable "replica_db_deletion_protection" {
  description = "Whether to enable deletion protection for read replica"
  type        = bool
  default     = false
}

variable "replica_db_skip_final_snapshot" {
  description = "Whether to skip final snapshot for read replica"
  type        = bool
  default     = true
}

# Parameter and Option Groups
variable "create_parameter_group" {
  description = "Whether to create a custom parameter group"
  type        = bool
  default     = true
}

variable "parameter_group_family" {
  description = "Parameter group family"
  type        = string
  default     = "sqlserver-ex-15.0"
}

variable "db_parameters" {
  description = "Database parameters"
  type = list(object({
    name  = string
    value = string
  }))
  default = []
}

variable "create_option_group" {
  description = "Whether to create a custom option group"
  type        = bool
  default     = true
}

variable "major_engine_version" {
  description = "Major engine version for option group"
  type        = string
  default     = "15.00"
}

variable "db_options" {
  description = "Database options"
  type = list(object({
    option_name = string
    option_settings = optional(list(object({
      name  = string
      value = string
    })), [])
  }))
  default = []
}

# Monitoring Configuration
variable "monitoring_interval" {
  description = "Enhanced monitoring interval in seconds"
  type        = number
  default     = 60
}

variable "enabled_cloudwatch_logs_exports" {
  description = "List of log types to export to CloudWatch"
  type        = list(string)
  default     = ["error"]
}

variable "performance_insights_enabled" {
  description = "Whether to enable Performance Insights"
  type        = bool
  default     = true
}

# =============================================================================
# DATABASE SUPPORT SERVICES VARIABLES
# =============================================================================

# =============================================================================
# REDIS CACHE VARIABLES
# =============================================================================

variable "redis_cache_name" {
  description = "Name of the Redis cache EC2 instance"
  type        = string
}

variable "redis_cache_ami_id" {
  description = "AMI ID for Redis cache instance"
  type        = string
}

variable "redis_cache_instance_type" {
  description = "Instance type for Redis cache"
  type        = string
}

variable "redis_cache_key_name" {
  description = "Key pair name for Redis cache instance"
  type        = string
  default     = null
}

variable "redis_cache_security_group_ids" {
  description = "Security group IDs for Redis cache instance"
  type        = list(string)
}

variable "redis_cache_subnet_id" {
  description = "Subnet ID for Redis cache deployment"
  type        = string
}

variable "redis_cache_availability_zone" {
  description = "Availability zone for Redis cache instance"
  type        = string
}

variable "redis_cache_private_ip_address" {
  description = "Private IP address for Redis cache instance"
  type        = string
  default     = null
}

variable "redis_cache_monitoring_enabled" {
  description = "Enable detailed monitoring for Redis cache instance"
  type        = bool
  default     = false
}

variable "redis_cache_ebs_optimized" {
  description = "Enable EBS optimization for Redis cache instance"
  type        = bool
  default     = true
}

variable "redis_cache_source_dest_check" {
  description = "Enable source destination check for Redis cache"
  type        = bool
  default     = true
}

variable "redis_cache_iam_instance_profile_name" {
  description = "IAM instance profile name for Redis cache instance"
  type        = string
  default     = null
}

variable "redis_cache_root_block_device" {
  description = "Root block device configuration for Redis cache instance"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "redis_cache_ebs_block_devices" {
  description = "Additional EBS block devices for Redis cache instance"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "redis_config" {
  description = "Redis configuration parameters"
  type = object({
    port                = number
    max_memory_mb       = number
    max_memory_policy   = string
    save_enabled        = bool
    password_enabled    = bool
    cluster_enabled     = bool
    bind_address        = string
  })
}

variable "redis_cloudwatch_log_group" {
  description = "CloudWatch log group for Redis cache logs"
  type        = string
}

variable "redis_enable_monitoring" {
  description = "Enable detailed monitoring and logging for Redis"
  type        = bool
  default     = false
}

variable "redis_log_retention_days" {
  description = "CloudWatch log retention period in days for Redis"
  type        = number
  default     = 7
}

# =============================================================================
# SMS SERVICE VARIABLES
# =============================================================================

variable "sms_service_name" {
  description = "Name of the SMS service EC2 instance"
  type        = string
}

variable "sms_service_ami_id" {
  description = "AMI ID for SMS service instance"
  type        = string
}

variable "sms_service_instance_type" {
  description = "Instance type for SMS service"
  type        = string
}

variable "sms_service_key_name" {
  description = "Key pair name for SMS service instance"
  type        = string
  default     = null
}

variable "sms_service_security_group_ids" {
  description = "Security group IDs for SMS service instance"
  type        = list(string)
}

variable "sms_service_subnet_id" {
  description = "Subnet ID for SMS service deployment"
  type        = string
}

variable "sms_service_availability_zone" {
  description = "Availability zone for SMS service instance"
  type        = string
}

variable "sms_service_private_ip_address" {
  description = "Private IP address for SMS service instance"
  type        = string
  default     = null
}

variable "sms_service_monitoring_enabled" {
  description = "Enable detailed monitoring for SMS service instance"
  type        = bool
  default     = false
}

variable "sms_service_ebs_optimized" {
  description = "Enable EBS optimization for SMS service instance"
  type        = bool
  default     = true
}

variable "sms_service_source_dest_check" {
  description = "Enable source destination check for SMS service"
  type        = bool
  default     = true
}

variable "sms_service_iam_instance_profile_name" {
  description = "IAM instance profile name for SMS service instance"
  type        = string
  default     = null
}

variable "sms_service_root_block_device" {
  description = "Root block device configuration for SMS service"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
}

variable "sms_service_ebs_volumes" {
  description = "EBS volume configuration for SMS service"
  type = map(object({
    device_name = string
    size        = number
    type        = string
    encrypted   = bool
    iops        = optional(number)
    throughput  = optional(number)
    kms_key_id  = optional(string)
    snapshot_id = optional(string)
    tags        = map(string)
  }))
}

variable "sms_service_ebs_block_devices" {
  description = "Additional EBS block devices for SMS service instance (legacy)"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "sms_service_additional_ebs_volumes" {
  description = "Additional EBS volumes for SMS service (created as separate resources)"
  type = map(object({
    device_name   = string
    size          = number
    type          = string
    encrypted     = bool
    kms_key_id    = optional(string)
    iops          = optional(number)
    throughput    = optional(number)
    snapshot_id   = optional(string)
  }))
  default = {}
}

variable "sms_config" {
  description = "SMS service configuration parameters"
  type = object({
    service_port            = number
    api_port                = number
    provider                = string
    rate_limit_per_minute   = number
    queue_size              = number
    retry_attempts          = number
    enable_ssl              = bool
    enable_authentication   = bool
  })
}

variable "sms_cloudwatch_log_group" {
  description = "CloudWatch log group for SMS service logs"
  type        = string
}

variable "sms_enable_monitoring" {
  description = "Enable detailed monitoring and logging for SMS service"
  type        = bool
  default     = false
}

variable "sms_log_retention_days" {
  description = "CloudWatch log retention period in days for SMS service"
  type        = number
  default     = 7
}

# =============================================================================
# DAM GATEWAY AZ1 VARIABLES
# =============================================================================

variable "dam_gateway_az1_name" {
  description = "Name of the DAM gateway AZ1 EC2 instance"
  type        = string
}

variable "dam_gateway_az1_ami_id" {
  description = "AMI ID for DAM gateway AZ1 instance"
  type        = string
}

variable "dam_gateway_az1_instance_type" {
  description = "Instance type for DAM gateway AZ1"
  type        = string
}

variable "dam_gateway_az1_key_name" {
  description = "Key pair name for DAM gateway AZ1 instance"
  type        = string
  default     = null
}

variable "dam_gateway_az1_security_group_ids" {
  description = "Security group IDs for DAM gateway AZ1 instance"
  type        = list(string)
}

variable "dam_gateway_az1_subnet_id" {
  description = "Subnet ID for DAM gateway AZ1 deployment"
  type        = string
}

variable "dam_gateway_az1_availability_zone" {
  description = "Availability zone for DAM gateway AZ1 instance"
  type        = string
}

variable "dam_gateway_az1_private_ip_address" {
  description = "Private IP address for DAM gateway AZ1 instance"
  type        = string
  default     = null
}

variable "dam_gateway_az1_monitoring_enabled" {
  description = "Enable detailed monitoring for DAM gateway AZ1 instance"
  type        = bool
  default     = false
}

variable "dam_gateway_az1_ebs_optimized" {
  description = "Enable EBS optimization for DAM gateway AZ1 instance"
  type        = bool
  default     = true
}

variable "dam_gateway_az1_source_dest_check" {
  description = "Enable source destination check for DAM gateway AZ1"
  type        = bool
  default     = true
}

variable "dam_gateway_az1_iam_instance_profile_name" {
  description = "IAM instance profile name for DAM gateway AZ1 instance"
  type        = string
  default     = null
}

variable "dam_gateway_az1_root_block_device" {
  description = "Root block device configuration for DAM gateway AZ1 instance"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dam_gateway_az1_ebs_block_devices" {
  description = "Additional EBS block devices for DAM gateway AZ1 instance"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dam_gateway_az1_config" {
  description = "DAM gateway AZ1 configuration parameters"
  type = object({
    gateway_port            = number
    management_port         = number
    agent_port              = number
    database_type           = string
    monitoring_enabled      = bool
    alert_enabled           = bool
    log_retention_days      = number
    enable_ssl              = bool
    enable_authentication   = bool
  })
}

variable "dam_gateway_az1_cloudwatch_log_group" {
  description = "CloudWatch log group for DAM gateway AZ1 logs"
  type        = string
}

variable "dam_gateway_az1_enable_monitoring" {
  description = "Enable detailed monitoring and logging for DAM gateway AZ1"
  type        = bool
  default     = false
}

variable "dam_gateway_az1_log_retention_days" {
  description = "CloudWatch log retention period in days for DAM gateway AZ1"
  type        = number
  default     = 30
}

# =============================================================================
# DAM GATEWAY AZ2 VARIABLES
# =============================================================================

variable "dam_gateway_az2_name" {
  description = "Name of the DAM gateway AZ2 EC2 instance"
  type        = string
}

variable "dam_gateway_az2_ami_id" {
  description = "AMI ID for DAM gateway AZ2 instance"
  type        = string
}

variable "dam_gateway_az2_instance_type" {
  description = "Instance type for DAM gateway AZ2"
  type        = string
}

variable "dam_gateway_az2_key_name" {
  description = "Key pair name for DAM gateway AZ2 instance"
  type        = string
  default     = null
}

variable "dam_gateway_az2_security_group_ids" {
  description = "Security group IDs for DAM gateway AZ2 instance"
  type        = list(string)
}

variable "dam_gateway_az2_subnet_id" {
  description = "Subnet ID for DAM gateway AZ2 deployment"
  type        = string
}

variable "dam_gateway_az2_availability_zone" {
  description = "Availability zone for DAM gateway AZ2 instance"
  type        = string
}

variable "dam_gateway_az2_private_ip_address" {
  description = "Private IP address for DAM gateway AZ2 instance"
  type        = string
  default     = null
}

variable "dam_gateway_az2_monitoring_enabled" {
  description = "Enable detailed monitoring for DAM gateway AZ2 instance"
  type        = bool
  default     = false
}

variable "dam_gateway_az2_ebs_optimized" {
  description = "Enable EBS optimization for DAM gateway AZ2 instance"
  type        = bool
  default     = true
}

variable "dam_gateway_az2_source_dest_check" {
  description = "Enable source destination check for DAM gateway AZ2"
  type        = bool
  default     = true
}

variable "dam_gateway_az2_iam_instance_profile_name" {
  description = "IAM instance profile name for DAM gateway AZ2 instance"
  type        = string
  default     = null
}

variable "dam_gateway_az2_root_block_device" {
  description = "Root block device configuration for DAM gateway AZ2 instance"
  type = object({
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  })
  default = null
}

variable "dam_gateway_az2_ebs_block_devices" {
  description = "Additional EBS block devices for DAM gateway AZ2 instance"
  type = list(object({
    device_name           = string
    volume_type           = string
    volume_size           = number
    delete_on_termination = bool
    encrypted             = bool
  }))
  default = []
}

variable "dam_gateway_az2_config" {
  description = "DAM gateway AZ2 configuration parameters"
  type = object({
    gateway_port            = number
    management_port         = number
    agent_port              = number
    database_type           = string
    monitoring_enabled      = bool
    alert_enabled           = bool
    log_retention_days      = number
    enable_ssl              = bool
    enable_authentication   = bool
  })
}

variable "dam_gateway_az2_cloudwatch_log_group" {
  description = "CloudWatch log group for DAM gateway AZ2 logs"
  type        = string
}

variable "dam_gateway_az2_enable_monitoring" {
  description = "Enable detailed monitoring and logging for DAM gateway AZ2"
  type        = bool
  default     = false
}

variable "dam_gateway_az2_log_retention_days" {
  description = "CloudWatch log retention period in days for DAM gateway AZ2"
  type        = number
  default     = 30
}

