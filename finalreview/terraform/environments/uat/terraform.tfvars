# Internet Compartment EC2 Instances

# SMTP Relay Configuration
smtp_relay_config = {
  instance_name            = "ez-vm-a-dev-smtp-001"  # [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
  ami_id                   = "ami-12345678"  # Replace with actual AMI ID
  instance_type            = "t3.medium"      # Cost-optimized instance type
  subnet_id                = "subnet-12345678"  # Replace with actual subnet ID
  security_group_ids       = ["sg-12345678"]   # Replace with actual security group IDs
  private_ip_address       = "*********"       # Replace with actual IP
  key_name                 = "infovault-key"
  iam_instance_profile_name = "smtp-relay-profile"
  monitoring_enabled       = true
  ebs_optimized           = true
  source_dest_check       = true

  # Storage Configuration
  root_block_device = {
    volume_type           = "gp3"
    volume_size           = 80
    delete_on_termination = true
    encrypted             = true
  }

  ebs_block_devices = [
    {
      device_name           = "/dev/sdb"
      volume_type           = "gp3"
      volume_size           = 100
      delete_on_termination = true
      encrypted             = true
    }
  ]
}

# Squid Proxy Configuration
squid_proxy = {
  instance_name = "ez-vm-a-dev-squid-001"  # [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
  ami_id        = "ami-047126e50991d067b"
  instance_type = "t3.medium"  # Cost-optimized instance type
  key_name      = "infovault-tooling-linux-key"
  subnet_id     = "subnet-01e871c49dc4b9b1f"
  security_group_ids = ["sg-0de73e7e87bb698d3", "sg-09dda82e79b4ad24c", "sg-054a1946b939e0eca"]
  private_ip_address = "**********"
  
  monitoring_enabled = true
  ebs_optimized     = true
  source_dest_check = false

  root_block_device = {
    volume_type           = "gp3"
    volume_size           = 20
    delete_on_termination = true
    encrypted             = true
  }

  ebs_block_devices = []

  squid_config = {
    port                    = 3128
    cache_dir_size_mb      = 1000
    maximum_object_size_mb = 50
    access_log_enabled     = true
    denied_sites           = []
    allowed_domains        = []
    client_subnets         = ["**********/16", "**********/16"]
  }
}

# CAM Ingress Configuration
cam_ingress = {
  instance_name = "ez-vm-a-dev-cam-001"  # [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
  ami_id        = "ami-09acd039e42ba3911"
  instance_type = "t3.medium"  # Cost-optimized instance type
  key_name      = "infovault-tooling-windows-key"
  subnet_id     = "subnet-01e871c49dc4b9b1f"
  security_group_ids = ["sg-054a1946b939e0eca"]
  private_ip_address = "**********"

  monitoring_enabled = true
  ebs_optimized     = true
  source_dest_check = true

  iam_instance_profile_name = "cam-ingress-profile"

  root_block_device = {
    volume_type           = "gp3"
    volume_size           = 30
    delete_on_termination = true
    encrypted             = true
  }

  ebs_block_devices = []

  cam_config = {
    service_port             = 8443
    admin_port               = 8444
    max_concurrent_sessions  = 100
    session_timeout_minutes  = 60
    audit_log_enabled        = true
    enable_ssl               = true
    enable_authentication    = true
    trusted_sources          = ["10.0.0.0/8", "**********/10"]
    allowed_authentication_methods = ["certificate", "password"]
  }
}

# Common Tags
common_tags = {
  Agency-Code   = "infovault"
  Project-Code  = "iv"
  Zone          = "ez"            # ez for Internet Zone
  Service       = "vm"            # Virtual Machine
  Az            = "a"             # Availability Zone a
  Environment   = "dev"           # Development environment
  CostCenter    = "CC-12345"     # Cost center code
  ManagedBy     = "terraform"     # Resource management tool
}

# Duplicate SMTP Relay Configuration removed - using the one from terraform-shriya.tfvars
# Duplicate Common Tags removed - using the one from terraform-shriya.tfvars

# InfoVault DEV Environment Configuration
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

# Environment Configuration
environment  = "dev"
aws_region   = "ap-southeast-1"
project_name = "infovault"

# S3 Bucket Configuration
s3_bucket_name   = "iz-sst-s3-uat-appdata-001"   # Following naming convention: [Zone]-[Service]-[Type]-[Env]-[workload]-[Running Number]
s3_force_destroy = true # Allow destruction in DEV environment

# S3 Versioning
enable_s3_versioning = true

# S3 Encryption
s3_encryption_algorithm = "AES256"
s3_kms_key_id           = null
s3_bucket_key_enabled   = true

# S3 Public Access Block (Security Best Practices)
s3_block_public_acls       = true
s3_block_public_policy     = true
s3_ignore_public_acls      = true
s3_restrict_public_buckets = true

# S3 Lifecycle Management
s3_lifecycle_enabled          = true
s3_transition_to_ia_days      = 30  # Move to Standard-IA after 30 days
s3_transition_to_glacier_days = 90  # Move to Glacier after 90 days
s3_expiration_days            = 365 # Delete after 1 year
s3_multipart_upload_days      = 7   # Clean up incomplete uploads after 7 days

# S3 Logging (Disabled for DEV)
s3_logging_enabled       = false
s3_logging_target_bucket = ""
s3_logging_target_prefix = "access-logs/"

# S3 Notifications (Disabled for DEV)
s3_notification_enabled = false

# =============================================================================
# NETWORKING CONFIGURATION
# =============================================================================

# Intranet Management Compartment VPC
intranet_management = {
  # VPC Configuration
  vpc_cidr                             = "**********/25"  # Updated as per vpc_subnet_details.json
  vpc_name                             = "iz-vpc-mgmt-ops"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = false # No IGW for management VPC

  # Private Subnets (Management VPC is primarily private)
  private_subnets = {
    mgmt_dlz1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-mgmt-ops-dlz1-01"
    }
    vpc_endpoint_dlz1 = {
      cidr_block                      = "***********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-mgmt-ops-eps-01"
    }
    vpc_endpoint_dlz2 = {
      cidr_block                      = "***********/28"
      availability_zone               = "ap-southeast-1b"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-b-mgmt-ops-eps-02"
    }
    managed_ad = {
      cidr_block                      = "***********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-mgmt-ops-mad-01"
    }
  }

  # No public subnets for management VPC
  public_subnets = {}

  # Route Tables
  private_route_tables = {
    mgmt_main_rt = {
      name = "iz-rt-mgmt-ops-prv-001"
    }
  }

  public_route_tables = {}

  # Route Table Associations
  private_subnet_route_associations = {
    mgmt_az1_assoc = {
      subnet_key      = "mgmt_az1"
      route_table_key = "mgmt_main_rt"
    }
  }

  public_subnet_route_associations = {}

  # No NAT Gateways needed
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes (to be configured based on actual peering connections)
  vpc_peering_routes = {}

  # Security Groups
  security_groups = {
    mgmt_sg = {
      name        = "iz-sgrp-mgmt-ops-001"
      description = "Management VPC Security Group"
    }
  }
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs (minimal configuration)
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints (to be configured based on actual endpoints)
  vpc_endpoints = {}
}

# Internet Facing Compartment VPC
internet_facing = {
  # VPC Configuration
  vpc_cidr                             = "**********/26"  # Updated as per vpc_subnet_details.json
  vpc_name                             = "ez-vpc-it-edge"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = true # IGW for internet access

  # Public Subnets for internet-facing resources
  public_subnets = {
nfw_dlz1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = true
      assign_ipv6_address_on_creation = false
      name                            = "ez-sub-a-it-edge-nfw-01"
    }
  }

  # Private Subnets for internal resources
  private_subnets = {
squid_egress_dlz1 = {
      cidr_block                      = "***********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "ez-sub-a-it-edge-sqd-01"
    }
  }

  # Route Tables
  public_route_tables = {
    public_rt = {
      name = "infovault-dev-public-rt"
    }
  }

  private_route_tables = {}

  # Route Table Associations
  public_subnet_route_associations = {
    public_az1_assoc = {
      subnet_key      = "public_az1"
      route_table_key = "public_rt"
    }
  }

  private_subnet_route_associations = {}

  # NAT Gateways (minimal configuration)
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes
  vpc_peering_routes = {}

  # Security Groups (minimal configuration)
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints
  vpc_endpoints = {}
}

# Gen Facing Compartment VPC
gen_facing = {
  # VPC Configuration
  vpc_cidr                             = "*********/25,**********/22"  # Primary and Secondary CIDRs
  vpc_name                             = "iz-vpc-uat-gen-workload"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = true

  # Public Subnets
  public_subnets = {
gen_squid_egress = {
      cidr_block                      = "*********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = true
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-gen-sqd-001"
    }
cam_ingress = {
      cidr_block                      = "**********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = true
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-gen-cam-001"
    }
ingress_alb = {
      cidr_block                      = "**********/27"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = true
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-gen-alb-001"
    }
  }

  # Private Subnets
  private_subnets = {
sftp_dm = {
      cidr_block                      = "**********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-gen-sftp-001"
    }
smtp = {
      cidr_block                      = "**********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-gen-smtp-001"
    }
app_dlz1 = {
      cidr_block                      = "**********/24"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-gen-app-001"
    }
app_dlz2 = {
      cidr_block                      = "**********/24"
      availability_zone               = "ap-southeast-1b"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-b-gen-app-002"
    }
db_dlz1 = {
      cidr_block                      = "**********/26"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-gen-db-001"
    }
db_dlz2 = {
      cidr_block                      = "***********/26"
      availability_zone               = "ap-southeast-1b"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-b-gen-db-002"
    }
  }

  # Route Tables
  public_route_tables = {
    gen_public_rt = {
      name = "iz-rt-gen-pub-001"
    }
  }

  private_route_tables = {
    gen_private_rt = {
      name = "iz-rt-gen-prv-001"
    }
  }

  # Route Table Associations
  public_subnet_route_associations = {
    gen_ingress_assoc = {
      subnet_key      = "gen_ingress_az1"
      route_table_key = "gen_public_rt"
    }
  }

  private_subnet_route_associations = {
    gen_egress_assoc = {
      subnet_key      = "gen_egress_az1"
      route_table_key = "gen_private_rt"
    }
    gen_migration_assoc = {
      subnet_key      = "gen_migration_az1"
      route_table_key = "gen_private_rt"
    }
  }

  # NAT Gateways
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes
  vpc_peering_routes = {}

  # Security Groups
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints
  vpc_endpoints = {}
}

# ABLRHF Compartment VPC
ablrhf = {
  # VPC Configuration
  vpc_cidr                             = "**********/28"  # Updated as per vpc_subnet_details.json
  vpc_name                             = "iz-vpc-logg-ablr"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = false # Private VPC

  # Private Subnets only
  private_subnets = {
    ablrhf_az1 = {
      cidr_block                      = "**********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-ablr-001"
    }
  }

  public_subnets = {}

  # Route Tables
  private_route_tables = {
    ablrhf_rt = {
      name = "iz-rt-ablr-prv-001"
    }
  }

  public_route_tables = {}

  # Route Table Associations
  private_subnet_route_associations = {
    ablrhf_assoc = {
      subnet_key      = "ablrhf_az1"
      route_table_key = "ablrhf_rt"
    }
  }

  public_subnet_route_associations = {}

  # No NAT Gateways
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes
  vpc_peering_routes = {}

  # Security Groups
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints
  vpc_endpoints = {}
}

# Patching Compartment VPC
patching = {
  # VPC Configuration
  vpc_cidr                             = "**********/27"  # Updated as per vpc_subnet_details.json
  vpc_name                             = "iz-vpc-it-patching"
  enable_dns_hostnames                 = true
  enable_dns_support                   = true
  enable_network_address_usage_metrics = false
  instance_tenancy                     = "default"
  create_internet_gateway              = false # Private VPC

  # Private Subnets only
  private_subnets = {
    patching = {
      cidr_block                      = "**********/28"
      availability_zone               = "ap-southeast-1a"
      map_public_ip_on_launch         = false
      assign_ipv6_address_on_creation = false
      name                            = "iz-sub-a-patch-01"
    }
  }

  public_subnets = {}

  # Route Tables
  private_route_tables = {
    patching_rt = {
      name = "iz-rt-patch-01"
    }
  }

  public_route_tables = {}

  # Route Table Associations
  private_subnet_route_associations = {
    patching_assoc = {
      subnet_key      = "patching_az1"
      route_table_key = "patching_rt"
    }
  }

  public_subnet_route_associations = {}

  # No NAT Gateways
  nat_gateways       = {}
  private_nat_routes = {}

  # VPC Peering Routes
  vpc_peering_routes = {}

  # Security Groups
  security_groups              = {}
  security_group_ingress_rules = {}
  security_group_egress_rules  = {}

  # Network ACLs
  network_acls              = {}
  network_acl_ingress_rules = {}
  network_acl_egress_rules  = {}

  # VPC Endpoints
  vpc_endpoints = {}
}

# Duplicate Common Tags removed - using the one from terraform-shriya.tfvars

# =============================================================================
# PATCHING WORKLOADS CONFIGURATION
# =============================================================================

# ============================================
# WSUS Server Configuration
# ============================================

# Based on existing infrastructure analysis of patch-wsus-01 (i-0b6e3b59a2aed8a51)
wsus_instance_name        = "iz-vm-a-patch-wsus-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
wsus_ami_id              = "ami-0c94d9a1b1234567890"  # Update with actual Windows Server 2019 AMI
wsus_instance_type       = "m5.large"  # 2 vCPU, 8GB RAM as per sizing doc
wsus_subnet_id           = "subnet-0dacd0ef604204014"  # Patching compartment subnet
wsus_security_group_ids  = ["sg-01f0c371bd11aa6f1"]  # Patching compartment security group
wsus_private_ip          = "**********"  # Static IP from existing infrastructure
wsus_iam_instance_profile = "WSUS-ServerRole"

# WSUS Configuration Parameters (based on existing configuration)
wsus_port                    = 8530
wsus_ssl_port               = 8531
wsus_content_dir            = "C:\\WSUS"
wsus_database_name          = "SUSDB"
wsus_update_languages       = ["en"]
wsus_update_classifications = [
  "Critical Updates",
  "Security Updates",
  "Updates",
  "Update Rollups",
  "Service Packs"
]
wsus_products = [
  "Windows Server 2019",
  "Windows Server 2022",
  "Windows 10",
  "Windows 11"
]

# WSUS Admin Configuration
wsus_admin_users         = ["wsusadmin", "patching-admin"]
wsus_admin_passwords     = {}  # Set via separate secure method
wsus_domain_join_enabled = false
wsus_domain_name         = ""
wsus_domain_user         = ""
wsus_domain_password     = ""

# WSUS Monitoring
wsus_cloudwatch_log_group = "/aws/ec2/wsus-server"
wsus_enable_monitoring    = true

# ============================================
# RHEL Repository Server Configuration
# ============================================

# Based on existing infrastructure analysis of patch-rhel-repo-01 (i-046cd06b02397e146)
rhel_instance_name        = "iz-vm-a-patch-rhel-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
rhel_ami_id              = "ami-0c02fb55956c7d316"  # Amazon Linux 2 AMI
rhel_instance_type       = "m5.large"  # 2 vCPU, 8GB RAM as per sizing doc
rhel_subnet_id           = "subnet-0dacd0ef604204014"  # Patching compartment subnet
rhel_security_group_ids  = ["sg-01f0c371bd11aa6f1"]  # Patching compartment security group
rhel_private_ip          = "**********"  # Static IP from existing infrastructure
rhel_iam_instance_profile = "RHEL-RepoServerRole"

# Repository Configuration Parameters
rhel_repository_port          = 80
rhel_repository_root          = "/var/www/html/repos"
rhel_repository_distributions = [
  "rhel8",
  "rhel9",
  "centos7",
  "centos8"
]
rhel_repository_architectures = ["x86_64", "noarch"]

# Upstream Repository Sync (initially empty, can be configured later)
rhel_upstream_repositories = []
rhel_sync_schedule        = "0 3 * * *"  # Daily at 3 AM

# RHEL Admin Configuration
rhel_admin_users     = ["repoadmin", "patching-admin"]
rhel_ssh_public_keys = [
  # Add SSH public keys here for admin access
]

# RHEL Monitoring
rhel_cloudwatch_log_group = "/aws/ec2/rhel-repository"
rhel_enable_monitoring    = true

# =============================================================================
# EKS CONFIGURATION VALUES
# =============================================================================

# EKS Cluster Configuration
cluster_name         = "iz-eks-dev-app-001"  # [Zone]-[Service]-[Env]-[workload]-[Running Number]
cluster_version      = "1.33"
cluster_role_name    = "iz-role-eks-cluster-001"  # [Zone]-[Service]-[Resource]-[Running Number]
node_group_role_name = "iz-role-eks-nodegroup-001"  # [Zone]-[Service]-[Resource]-[Running Number]

# VPC Configuration for EKS (Update these with actual subnet and security group IDs)
subnet_ids                 = []
cluster_security_group_ids = []
endpoint_private_access    = true
endpoint_public_access     = true
public_access_cidrs        = ["0.0.0.0/0"]
cluster_log_types          = ["api", "audit", "authenticator", "controllerManager", "scheduler"]
kms_key_arn                = null

# Node Group Configuration - Multi-AZ Deployment
node_group_name           = "iz-eks-ng-dev-app-001"  # [Zone]-[Service]-[Type]-[Env]-[workload]-[Running Number]
node_group_subnet_ids     = [
  "subnet-023cafbedbb31d13e",  # app-subnet-az1 (DLZ1) - ap-southeast-1a
  "subnet-015e4b2acb87091d9"   # app-subnet-az2 (DLZ2) - ap-southeast-1b
]
node_group_instance_types = ["m5.2xlarge"]  # 8 vCPU, 32GB RAM as per sizing doc
node_group_ami_type       = "CUSTOM"
node_group_capacity_type  = "ON_DEMAND"
node_group_disk_size      = 250  # 250GB storage as per sizing doc
node_group_desired_size   = 2  # Ensure at least 1 node per AZ
node_group_max_size       = 6  # Allow scaling across both AZs
node_group_min_size       = 2  # Minimum 2 nodes to ensure multi-AZ distribution
node_group_labels         = { 
  "environment" = "dev"
  "role"        = "application"
  "multi-az"    = "enabled"
}

# EKS Addon Versions (from ecosystem data)
addon_amazon_cloudwatch_observability_version = "v2.2.0-eksbuild.1"
addon_aws_ebs_csi_driver_version              = "v2.42.0-eksbuild.1"
addon_aws_guardduty_agent_version             = "v1.9.1-eksbuild.1"
addon_coredns_version                         = "v1.12.0-eksbuild.1"
addon_eks_pod_identity_agent_version          = "v1.5.1-eksbuild.1"
addon_kube_proxy_version                      = "v1.33.0-eksbuild.1"
addon_vpc_cni_version                         = "v1.19.0-eksbuild.1"
# infovault-dev-gitlab-runner configuration
infovault_dev_gitlab_runner_name                      = "iz-vm-a-mgmt-gitlab-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
infovault_dev_gitlab_runner_ami_id                    = "*********************"
infovault_dev_gitlab_runner_instance_type             = "m5.large"  # 2 vCPU, 8GB RAM as per sizing doc
infovault_dev_gitlab_runner_key_name                  = null
infovault_dev_gitlab_runner_security_group_ids        = ["sg-00df94fe66c7464f5"]
infovault_dev_gitlab_runner_subnet_id                 = "subnet-07ab4b059d69e4044"
infovault_dev_gitlab_runner_iam_instance_profile_name = "infovault-dev-ec2-profile"
infovault_dev_gitlab_runner_availability_zone         = "ap-southeast-1a"
infovault_dev_gitlab_runner_monitoring_enabled        = false
infovault_dev_gitlab_runner_ebs_optimized             = false
infovault_dev_gitlab_runner_source_dest_check         = true
infovault_dev_gitlab_runner_private_ip_address        = "**********"
infovault_dev_gitlab_runner_root_block_device         = null
infovault_dev_gitlab_runner_ebs_block_devices         = []

# infovault-dev-linux-tooling-server configuration
infovault_dev_linux_tooling_server_name                      = "iz-vm-a-mgmt-linux-tooling-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
infovault_dev_linux_tooling_server_ami_id                    = "*********************"
infovault_dev_linux_tooling_server_instance_type             = "t3.medium"
infovault_dev_linux_tooling_server_key_name                  = null
infovault_dev_linux_tooling_server_security_group_ids        = ["sg-095117b1a793c2ce0"]
infovault_dev_linux_tooling_server_subnet_id                 = "subnet-07ab4b059d69e4044"
infovault_dev_linux_tooling_server_iam_instance_profile_name = "infovault-dev-ec2-profile"
infovault_dev_linux_tooling_server_availability_zone         = "ap-southeast-1a"
infovault_dev_linux_tooling_server_monitoring_enabled        = false
infovault_dev_linux_tooling_server_ebs_optimized             = false
infovault_dev_linux_tooling_server_source_dest_check         = true
infovault_dev_linux_tooling_server_private_ip_address        = "**********"
infovault_dev_linux_tooling_server_root_block_device         = null
infovault_dev_linux_tooling_server_ebs_block_devices         = []

# mgmt-newgenadm-win-tooling-01 configuration
mgmt_newgenadm_win_tooling_01_name                      = "iz-vm-a-mgmt-win-tooling-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
mgmt_newgenadm_win_tooling_01_ami_id                    = "ami-0482455c4148846e3"
mgmt_newgenadm_win_tooling_01_instance_type             = "t3.xlarge"
mgmt_newgenadm_win_tooling_01_key_name                  = "infovault-tooling-windows-key"
mgmt_newgenadm_win_tooling_01_security_group_ids        = ["sg-095117b1a793c2ce0"]
mgmt_newgenadm_win_tooling_01_subnet_id                 = "subnet-07ab4b059d69e4044"
mgmt_newgenadm_win_tooling_01_iam_instance_profile_name = "infovault-dev-ec2-profile"
mgmt_newgenadm_win_tooling_01_availability_zone         = "ap-southeast-1a"
mgmt_newgenadm_win_tooling_01_monitoring_enabled        = false
mgmt_newgenadm_win_tooling_01_ebs_optimized             = false
mgmt_newgenadm_win_tooling_01_source_dest_check         = true
mgmt_newgenadm_win_tooling_01_private_ip_address        = "**********"
mgmt_newgenadm_win_tooling_01_root_block_device         = null
mgmt_newgenadm_win_tooling_01_ebs_block_devices         = []

# dev-management-server-avm150-new configuration
dev_management_server_avm150_new_name                      = "iz-vm-a-mgmt-avm150-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
dev_management_server_avm150_new_ami_id                    = "*********************"
dev_management_server_avm150_new_instance_type             = "c6a.xlarge"
dev_management_server_avm150_new_key_name                  = null
dev_management_server_avm150_new_security_group_ids        = ["sg-0f2fdd192120dbf17"]
dev_management_server_avm150_new_subnet_id                 = "subnet-07ab4b059d69e4044"
dev_management_server_avm150_new_iam_instance_profile_name = "dev-dam-management-servers-profile"
dev_management_server_avm150_new_availability_zone         = "ap-southeast-1a"
dev_management_server_avm150_new_monitoring_enabled        = false
dev_management_server_avm150_new_ebs_optimized             = false
dev_management_server_avm150_new_source_dest_check         = true
dev_management_server_avm150_new_private_ip_address        = "***********"
dev_management_server_avm150_new_root_block_device         = null
dev_management_server_avm150_new_ebs_block_devices         = []

# dev-dra-admin-server-new configuration
dev_dra_admin_server_new_name                      = "iz-vm-a-mgmt-dra-admin-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
dev_dra_admin_server_new_ami_id                    = "*********************"
dev_dra_admin_server_new_instance_type             = "m5.xlarge"  # 4 vCPU, 16GB RAM as per sizing doc
dev_dra_admin_server_new_key_name                  = null
dev_dra_admin_server_new_security_group_ids        = ["sg-0b67c6b7904908adf"]
dev_dra_admin_server_new_subnet_id                 = "subnet-07ab4b059d69e4044"
dev_dra_admin_server_new_iam_instance_profile_name = "dev-dam-management-servers-profile"
dev_dra_admin_server_new_availability_zone         = "ap-southeast-1a"
dev_dra_admin_server_new_monitoring_enabled        = false
dev_dra_admin_server_new_ebs_optimized             = false
dev_dra_admin_server_new_source_dest_check         = true
dev_dra_admin_server_new_private_ip_address        = "**********"
dev_dra_admin_server_new_root_block_device         = null
dev_dra_admin_server_new_ebs_block_devices         = []

# dev-dra-analytics-server-new configuration
dev_dra_analytics_server_new_name                      = "iz-vm-a-mgmt-dra-analytics-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
dev_dra_analytics_server_new_ami_id                    = "*********************"
dev_dra_analytics_server_new_instance_type             = "m5.xlarge"  # 4 vCPU, 16GB RAM as per sizing doc
dev_dra_analytics_server_new_key_name                  = null
dev_dra_analytics_server_new_security_group_ids        = ["sg-0d7508facd515c5ea"]
dev_dra_analytics_server_new_subnet_id                 = "subnet-07ab4b059d69e4044"
dev_dra_analytics_server_new_iam_instance_profile_name = "dev-dam-management-servers-profile"
dev_dra_analytics_server_new_availability_zone         = "ap-southeast-1a"
dev_dra_analytics_server_new_monitoring_enabled        = false
dev_dra_analytics_server_new_ebs_optimized             = false
dev_dra_analytics_server_new_source_dest_check         = true
dev_dra_analytics_server_new_private_ip_address        = "***********"
dev_dra_analytics_server_new_root_block_device         = null
dev_dra_analytics_server_new_ebs_block_devices         = []

# dev-management-server-avm150-imperva configuration
dev_management_server_avm150_imperva_name                      = "iz-vm-a-mgmt-avm150-imperva-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
dev_management_server_avm150_imperva_ami_id                    = "ami-0d23603cba346ca65"
dev_management_server_avm150_imperva_instance_type             = "m5.2xlarge"  # 8 vCPU, 32GB RAM as per sizing doc
dev_management_server_avm150_imperva_key_name                  = "dam-key"
dev_management_server_avm150_imperva_security_group_ids        = ["sg-0f2fdd192120dbf17"]
dev_management_server_avm150_imperva_subnet_id                 = "subnet-07ab4b059d69e4044"
dev_management_server_avm150_imperva_iam_instance_profile_name = "dev-dam-management-servers-profile"
dev_management_server_avm150_imperva_availability_zone         = "ap-southeast-1a"
dev_management_server_avm150_imperva_monitoring_enabled        = false
dev_management_server_avm150_imperva_ebs_optimized             = true
dev_management_server_avm150_imperva_source_dest_check         = true
dev_management_server_avm150_imperva_private_ip_address        = "**********"
dev_management_server_avm150_imperva_root_block_device         = null
dev_management_server_avm150_imperva_ebs_block_devices         = []

# ad-tooling-windows-02 configuration
ad_tooling_windows_02_name                      = "iz-vm-a-mgmt-win-ad-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
ad_tooling_windows_02_ami_id                    = "ami-0482455c4148846e3"
ad_tooling_windows_02_instance_type             = "t3.xlarge"
ad_tooling_windows_02_key_name                  = "infovault-tooling-windows-key"
ad_tooling_windows_02_security_group_ids        = ["sg-0511f8964219f7537"]
ad_tooling_windows_02_subnet_id                 = "subnet-0ebbc016c2fc6b3db"
ad_tooling_windows_02_iam_instance_profile_name = "infovault-dev-ec2-profile"
ad_tooling_windows_02_availability_zone         = "ap-southeast-1a"
ad_tooling_windows_02_monitoring_enabled        = false
ad_tooling_windows_02_ebs_optimized             = false
ad_tooling_windows_02_source_dest_check         = true
ad_tooling_windows_02_private_ip_address        = "***********"
ad_tooling_windows_02_root_block_device         = null
ad_tooling_windows_02_ebs_block_devices         = []

# =============================================================================
# INTERNET FACING COMPUTE CONFIGURATION
# =============================================================================

# Duplicate Squid Proxy Configuration removed - using the one from terraform-shriya.tfvars

# Bastion Host Configuration
bastion_host = {
  instance_name              = "ez-vm-a-net-bastion-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
  ami_id                     = "ami-09acd039e42ba3911"
  instance_type              = "t3.medium"
  key_name                   = "infovault-tooling-windows-key"
  subnet_id                  = "subnet-01e871c49dc4b9b1f"
  security_group_ids         = ["sg-054a1946b939e0eca"]
  availability_zone          = "ap-southeast-1a"
  private_ip_address         = "**********"
  platform                   = "windows"
  monitoring_enabled         = false
  ebs_optimized              = true
  source_dest_check          = true
  associate_public_ip_address = true
  
  # Use existing IAM resources
  create_iam_role                        = false
  create_iam_instance_profile            = false
  existing_iam_instance_profile_name     = "MyEC2SSMRole"
  
  # Storage configuration
  root_block_device = {
    volume_type           = "gp3"
    volume_size           = 30
    delete_on_termination = true
    encrypted             = true
  }
  
  ebs_block_devices = []
}

# =============================================================================
# EKS VPC ENDPOINTS CONFIGURATION
# =============================================================================

# VPC Endpoints for EKS in Gen-Facing Compartment
eks_vpc_endpoints = {
  # Endpoint Names following [Zone]-[Service]-[Type]-[Running Number]
  endpoint_names = {
    ec2 = "iz-vpce-ec2-001"
    ecr_api = "iz-vpce-ecr-api-001"
    ecr_dkr = "iz-vpce-ecr-dkr-001"
    eks = "iz-vpce-eks-001"
    s3 = "iz-vpce-s3-001"
    ssm = "iz-vpce-ssm-001"
    guardduty = "iz-vpce-gd-001"
  }
  # EKS Application subnets - subnet-023cafbedbb31d13e, subnet-015e4b2acb87091d9
  subnet_ids = [
    "subnet-023cafbedbb31d13e",  # infovault-dev-gen-facing-compartment-app-subnet-DLZ1
    "subnet-015e4b2acb87091d9"   # infovault-dev-gen-facing-compartment-app-subnet-DLZ2
  ]
  
  # SSM endpoints typically in management subnets
  ssm_subnet_ids = [
    "subnet-0a1802eb99e96cbed"   # Management subnet for SSM endpoints
  ]
  
  # Route table IDs for S3 Gateway endpoint (include all route tables in gen-facing VPC)
  route_table_ids = [
    "rtb-015ae5889ca4aaadd",
    "rtb-0795b2e3d3e3f88ed",
    "rtb-038d861f618d80392",
    "rtb-0a3ed8d3c391e4cca",
    "rtb-08edcd5b7304aa2aa",
    "rtb-06ca622706fb0676d",
    "rtb-0e47b2c134f9ec024",
    "rtb-0c7da122446383cc5",
    "rtb-04dea60d5aeca9def",
    "rtb-0d508af52f5965f6a",
    "rtb-0f6307e8c214b6a71",
    "rtb-098958cd03a46c219"
  ]
  
  # Enable all endpoints to match current production setup
  enable_ec2_endpoint       = true
  enable_ecr_endpoints      = true
  enable_eks_endpoint       = true
  enable_s3_endpoint        = true
  enable_ssm_endpoints      = true
  enable_guardduty_endpoint = true
}

# =============================================================================
# IAM ROLES FOR SERVICE ACCOUNTS (IRSA) CONFIGURATION
# =============================================================================

# IRSA Role Names (matching current production setup)
irsa = {
  aws_load_balancer_controller_role_name = "iz-role-eks-alb-001"  # [Zone]-[Service]-[Resource]-[Running Number]
  cert_manager_role_name                 = "iz-role-eks-cert-001"  # [Zone]-[Service]-[Resource]-[Running Number]
  cluster_autoscaler_role_name           = "iz-role-eks-autoscaler-001"  # [Zone]-[Service]-[Resource]-[Running Number]
  external_dns_role_name                 = "iz-role-eks-dns-001"  # [Zone]-[Service]-[Resource]-[Running Number]
}

# =============================================================================
# EKS NODE POLICIES CONFIGURATION
# =============================================================================

# Custom IAM Policies for EKS Nodes (matching current production setup)
eks_node_policies = {
  s3_access_policy_name      = "iz-policy-eks-s3-001"  # [Zone]-[Service]-[Resource]-[Running Number]
  kms_access_policy_name     = "iz-policy-eks-kms-001"  # [Zone]-[Service]-[Resource]-[Running Number]
  attach_to_node_group_role  = true
  attach_additional_policies = true
}

# =============================================================================
# KUBERNETES CONTROLLERS CONFIGURATION
# =============================================================================

# Kubernetes Controllers Configuration
kubernetes_controllers = {
  # AWS Load Balancer Controller
  enable_aws_load_balancer_controller  = true
  aws_load_balancer_controller_version = "1.8.1"
  
  # Certificate Manager
  enable_cert_manager                  = true
  cert_manager_version                 = "v1.13.3"
  enable_cert_manager_pca_issuer       = true
  cert_manager_pca_issuer_version      = "v1.2.6"
  
  # Cluster Autoscaler
  enable_cluster_autoscaler            = true
  cluster_autoscaler_version           = "9.34.1"
  
  # External DNS
  enable_external_dns                  = true
  external_dns_version                 = "1.14.3"
}

# =============================================================================
# KUBERNETES INFRASTRUCTURE CONFIGURATION
# =============================================================================

# Storage Configuration
create_shared_storage = false
shared_storage_size   = "10Gi"

# Infrastructure Configuration
infrastructure_config = {
  log_level    = "INFO"
  environment  = "dev"
  region       = "ap-southeast-1"
}

# =============================================================================
# DATABASE INFRASTRUCTURE CONFIGURATION
# =============================================================================

# Database Network Configuration
database_vpc_id = "vpc-05e0e8104b3321c40"  # infovault-dev-gen-facing-compartment
database_subnet_dlz1_id = "subnet-0d4f42cf30b20c42d"  # db-subnet-az1 (DLZ1)
database_subnet_dlz2_id = "subnet-0c775a4f57dfa0e61"  # db-subnet-az2 (DLZ2)
management_subnet_cidr = "**********/28"  # Management subnet for database access

# Database Security Configuration
create_application_security_group = true
database_port = 1433  # SQL Server default port

# Additional security rules for database access
additional_database_ingress_rules = {
  sql_server_from_eks = {
    from_port   = 1433
    to_port     = 1433
    protocol    = "tcp"
    description = "SQL Server access from EKS application subnets"
    cidr_blocks = ["**********/24", "**********/24"]  # app-subnet-az1 and app-subnet-az2
  }
}

database_egress_rules = {
  all_outbound = {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    description = "All outbound traffic"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Application security group rules for database connectivity
application_ingress_rules = {
  http_from_alb = {
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    description = "HTTP traffic from ALB to applications"
    cidr_blocks = ["**********/16"]
  }
  https_from_alb = {
    from_port   = 8443
    to_port     = 8443
    protocol    = "tcp"
    description = "HTTPS traffic from ALB to applications"
    cidr_blocks = ["**********/16"]
  }
}

# Enhanced monitoring and connection configuration
create_monitoring_role = true
create_connection_secret = true
existing_database_endpoint = ""  # Will be populated after database creation
database_name = "InfoVaultDB"
database_username = "sa"

# =============================================================================
# PRIMARY DATABASE CONFIGURATION (SQL SERVER)
# =============================================================================

# Primary Database Instance Configuration
create_primary_database = true
primary_db_engine = "sqlserver-ex"
primary_db_engine_version = "15.00.4073.23.v1"  # SQL Server 2019
primary_db_instance_class = "db.t3.large"  # Cost-optimized for development

# Database Configuration
primary_db_identifier = "iz-dbs-a-uat-mssql-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
primary_db_name = null  # SQL Server Express doesn't support custom database names at creation
primary_db_master_username = "sa"
manage_master_user_password = true
primary_db_parameter_group_name = "iz-dbs-param-uat-001"  # Following naming convention
primary_db_subnet_group_name = "iz-dbs-subnet-uat-001"  # Following naming convention

# Storage Configuration
primary_db_allocated_storage = 100  # 100 GB initial storage
primary_db_max_allocated_storage = 500  # Auto-scaling up to 500 GB
primary_db_storage_type = "gp3"
primary_db_storage_encrypted = true
primary_db_kms_key_id = null  # Use default AWS KMS key

# Network and Security Configuration
primary_db_publicly_accessible = false  # Private database
primary_db_multi_az = true  # Multi-AZ for high availability

# Backup and Maintenance Configuration
primary_db_backup_retention_period = 7  # 7 days backup retention
primary_db_backup_window = "03:00-04:00"  # 3-4 AM UTC backup window
primary_db_maintenance_window = "sun:04:00-sun:05:00"  # Sunday 4-5 AM UTC maintenance

# Security and Protection
primary_db_deletion_protection = true  # Prevent accidental deletion
primary_db_skip_final_snapshot = false  # Take final snapshot on deletion

# =============================================================================
# READ REPLICA CONFIGURATION (OPTIONAL)
# =============================================================================

# Read Replica Configuration (for reporting/analytics workloads)
create_read_replica = false  # Start without read replica, can be enabled later
replica_db_engine = "sqlserver-ex"
replica_db_engine_version = "15.00.4073.23.v1"
replica_db_instance_class = "db.t3.medium"  # Smaller instance for read operations
replica_db_master_username = "sa"
replica_db_allocated_storage = 100
replica_db_max_allocated_storage = 300
replica_db_storage_type = "gp3"
replica_db_storage_encrypted = true
replica_db_publicly_accessible = false
replica_db_multi_az = false  # Single AZ for cost optimization
replica_db_maintenance_window = "sun:05:00-sun:06:00"
replica_db_deletion_protection = false
replica_db_skip_final_snapshot = true

# =============================================================================
# DATABASE PARAMETER AND OPTION GROUPS
# =============================================================================

# Parameter Group Configuration
create_parameter_group = true
parameter_group_family = "sqlserver-ex-15.0"
db_parameters = [
  {
    name  = "backup compression default"
    value = "1"
  },
  {
    name  = "cost threshold for parallelism"
    value = "50"
  }
]

# Option Group Configuration
create_option_group = true
major_engine_version = "15.00"
db_options = []  # SQL Server Express has limited options

# =============================================================================
# DATABASE MONITORING CONFIGURATION
# =============================================================================

# Enhanced Monitoring Configuration
monitoring_interval = 60  # 60 seconds enhanced monitoring
enabled_cloudwatch_logs_exports = ["error", "agent"]  # Export error and agent logs
performance_insights_enabled = true  # Enable Performance Insights

# =============================================================================
# SQL SERVER CONFIGURATION
# =============================================================================

# SQL Server Common Configuration
sql_server_ami_id = "ami-0d0daa2cb01e4f274"  # Windows Server 2022 with SQL Server 2022
sql_server_port = 1433
sql_server_version = "2022"
sql_server_edition = "Standard"
sql_server_instance_name = "MSSQLSERVER"
sql_server_collation = "SQL_Latin1_General_CP1_CI_AS"

# Primary SQL Server (mssql-primary)
primary_instance_name = "mssql-primary"
primary_instance_type = "r5.2xlarge"  # 8 vCPU, 64GB RAM for primary
primary_private_ip = "***********"
sql_server_ag_name = "infovault-ag"
sql_server_ag_ip = "***********0"  # Virtual IP for AG listener

# Secondary SQL Server (mssql-secondary)
secondary_instance_name = "mssql-secondary"
secondary_instance_type = "r5.2xlarge"  # Same size as primary for HA
secondary_private_ip = "***********"

# Read Replica SQL Server (mssql-read-replica)
read_replica_instance_name = "mssql-read-replica"
read_replica_instance_type = "r5.xlarge"  # 4 vCPU, 32GB RAM for read replica
read_replica_private_ip = "***********"

# Common SQL Server Configuration
sql_server_ebs_volumes = {
  backup = {
    device_name = "/dev/sdf"
    size = 500
    type = "gp3"
    encrypted = true
    iops = 12000
    throughput = 500
  }
  data = {
    device_name = "/dev/sdg"
    size = 1000
    type = "gp3"
    encrypted = true
    iops = 16000
    throughput = 1000
  }
  log = {
    device_name = "/dev/sdh"
    size = 200
    type = "gp3"
    encrypted = true
    iops = 12000
    throughput = 500
  }
  temp = {
    device_name = "/dev/sdi"
    size = 100
    type = "gp3"
    encrypted = true
    iops = 6000
    throughput = 300
  }
}

# SQL Server Network Configuration
sql_server_subnet_dlz1_id = "subnet-0d4f42cf30b20c42d"  # DLZ1 subnet
sql_server_subnet_dlz2_id = "subnet-0c775a4f57dfa0e61"  # DLZ2 subnet
sql_server_security_group_ids = ["sg-0de73e7e87bb698d3"]  # Database security group

# SQL Server Monitoring
sql_server_cloudwatch_log_group = "/aws/ec2/sqlserver"
sql_server_enable_monitoring = true
sql_server_log_retention_days = 30

# =============================================================================
# APPLICATION SUPPORT SERVICES
# =============================================================================

# NFS Server Configuration
nfs_server_name = "iz-vm-a-gen-nfs-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
nfs_server_ami_id = "ami-05839df7da37a9345"
nfs_server_instance_type = "t3.large"
nfs_server_subnet_id = "subnet-023cafbedbb31d13e"
nfs_server_security_group_ids = ["sg-095c31fa3b94c41ae"]
nfs_server_key_name = "infovault-nfs-key"
nfs_server_iam_instance_profile_name = "infovault-dev-ec2-profile"
nfs_server_private_ip_address = "************"
nfs_server_monitoring_enabled = true
nfs_server_ebs_optimized = true
nfs_server_source_dest_check = true
nfs_server_user_data_script = "nfs_userdata.sh"
nfs_server_user_data_vars = {}

nfs_server_root_block_device = {
  volume_type           = "gp3"
  volume_size           = 30
  delete_on_termination = false
  encrypted             = true
}

nfs_server_ebs_block_devices = [
  {
    device_name           = "/dev/sdf"
    volume_type           = "gp3"
    volume_size           = 500
    delete_on_termination = false
    encrypted             = true
  }
]

# Migration Server Configuration
migration_server_name = "iz-vm-a-gen-migration-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
migration_server_user_data_script = "migration_userdata.ps1"
migration_server_user_data_vars = {}
migration_server_ami_id = "ami-0482455c4148846e3"
migration_server_instance_type = "t3.xlarge"
migration_server_subnet_id = "subnet-023cafbedbb31d13e"
migration_server_security_group_ids = ["sg-0c081d79227db7d86"]
migration_server_key_name = "infovault-migration-server-key"
migration_server_iam_instance_profile_name = "InfoVault-MigrationServer-SSM-Role-Profile"
migration_server_private_ip_address = "***********"
migration_server_monitoring_enabled = true
migration_server_ebs_optimized = true
migration_server_source_dest_check = true

migration_server_root_block_device = {
  volume_type           = "gp3"
  volume_size           = 50
  delete_on_termination = false
  encrypted             = true
}

migration_server_ebs_block_devices = [
  {
    device_name           = "/dev/sdf"
    volume_type           = "gp3"
    volume_size           = 1000
    delete_on_termination = false
    encrypted             = true
  }
]

# =============================================================================
# DATABASE SUPPORT SERVICES CONFIGURATION
# =============================================================================

# Based on existing EC2 instances in database subnets DLZ1 and DLZ2
# Subnet mapping: DLZ1 = subnet-0d4f42cf30b20c42d, DLZ2 = subnet-0c775a4f57dfa0e61

# =============================================================================
# REDIS CACHE CONFIGURATION (ec2-redis-cache-01)
# =============================================================================

# Redis Cache Instance Configuration
redis_cache_name = "iz-vm-a-gen-redis-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
redis_cache_ami_id = "*********************"  # Amazon Linux 2 AMI
redis_cache_instance_type = "r5.xlarge"  # 4 vCPU, 32GB RAM as per sizing doc
redis_cache_tags = {
  Name          = "iz-vm-a-gen-redis-001"
  Agency-Code   = "infovault"
  Project-Code  = "iv"
  Zone          = "iz"
  Service       = "vm"
  Az            = "a"
  Environment   = "dev"
  Workloads     = "redis"
  Running-Number = "001"
  CostCenter    = "CC-12345"
  ManagedBy     = "terraform"
}
redis_cache_key_name = null
redis_cache_security_group_ids = ["sg-0de73e7e87bb698d3"]  # Database security group
redis_cache_subnet_id = "subnet-0d4f42cf30b20c42d"  # DLZ1 subnet
redis_cache_availability_zone = "ap-southeast-1a"
redis_cache_private_ip_address = "**********"  # Static IP assignment
redis_cache_monitoring_enabled = true
redis_cache_ebs_optimized = true
redis_cache_source_dest_check = true
redis_cache_iam_instance_profile_name = "infovault-dev-ec2-profile"

# Redis Cache Storage Configuration
redis_cache_root_block_device = {
  volume_type           = "gp3"
  volume_size           = 30
  delete_on_termination = true
  encrypted             = true
}

redis_cache_ebs_block_devices = [
  {
    device_name           = "/dev/xvdf"
    volume_type           = "gp3"
    volume_size           = 100
    delete_on_termination = true
    encrypted             = true
  }
]

# Redis Configuration Parameters
redis_config = {
  port                = 6379
  max_memory_mb       = 2048
  max_memory_policy   = "allkeys-lru"
  save_enabled        = true
  password_enabled    = true
  cluster_enabled     = false
  bind_address        = "0.0.0.0"
}

# Redis Monitoring Configuration
redis_cloudwatch_log_group = "/aws/ec2/redis-cache"
redis_enable_monitoring = true
redis_log_retention_days = 14

# =============================================================================
# SMS SERVICE CONFIGURATION (ec2-sms-01)
# =============================================================================

# SMS Service Instance Configuration
sms_service_name = "iz-vm-a-gen-sms-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
sms_service_ami_id = "*********************"  # Amazon Linux 2 AMI
sms_service_instance_type = "r5.xlarge"  # 4 vCPU, 32GB RAM as per sizing doc
sms_service_tags = {
  Name          = "iz-vm-a-gen-sms-001"
  Agency-Code   = "infovault"
  Project-Code  = "iv"
  Zone          = "iz"
  Service       = "vm"
  Az            = "a"
  Environment   = "dev"
  Workloads     = "sms"
  Running-Number = "001"
  CostCenter    = "CC-12345"
  ManagedBy     = "terraform"
}
sms_service_key_name = null
sms_service_security_group_ids = ["sg-0de73e7e87bb698d3"]  # Database security group
sms_service_subnet_id = "subnet-0d4f42cf30b20c42d"  # DLZ1 subnet
sms_service_availability_zone = "ap-southeast-1a"
sms_service_private_ip_address = "**********"  # Static IP assignment
sms_service_monitoring_enabled = true
sms_service_ebs_optimized = true
sms_service_source_dest_check = true
sms_service_iam_instance_profile_name = "infovault-dev-ec2-profile"

# SMS Service Storage Configuration
sms_service_root_block_device = {
  volume_type           = "gp3"
  volume_size           = 20
  delete_on_termination = true
  encrypted             = true
}

sms_service_ebs_block_devices = []

# Additional EBS Volumes for SMS Service
sms_service_ebs_volumes = {
  data = {
    device_name = "/dev/xvdf"
    size        = 125
    type        = "gp3"
    encrypted   = true
    iops        = 3000
    throughput  = 125
    kms_key_id  = null
    tags = {
      Name          = "iz-bst-a-gen-sms-data-001"
      Agency-Code   = "infovault"
      Project-Code  = "iv"
      Zone          = "iz"
      Service       = "bst"
      Az            = "a"
      Environment   = "gen"
      Workloads     = "sms"
      Running-Number = "001"
      Type          = "data"
    }
  },
logs = {
    device_name = "/dev/xvdg"
    size        = 200
    type        = "gp3"
    encrypted   = true
    iops        = 3000
    throughput  = 125
    kms_key_id  = null
    tags = {
      Name          = "iz-bst-a-gen-sms-logs-001"
      Agency-Code   = "infovault"
      Project-Code  = "iv"
      Zone          = "iz"
      Service       = "bst"
      Az            = "a"
      Environment   = "gen"
      Workloads     = "sms"
      Running-Number = "001"
      Type          = "logs"
    }
  },
backup = {
    device_name = "/dev/xvdh"
    size        = 200
    type        = "gp3"
    encrypted   = true
    iops        = 3000
    throughput  = 125
    kms_key_id  = null
    tags = {
      Name          = "iz-bst-a-gen-sms-backup-001"
      Agency-Code   = "infovault"
      Project-Code  = "iv"
      Zone          = "iz"
      Service       = "bst"
      Az            = "a"
      Environment   = "gen"
      Workloads     = "sms"
      Running-Number = "001"
      Type          = "backup"
    }
  }
}

# SMS Service Configuration Parameters
sms_config = {
  service_port            = 3000
  api_port                = 3001
  provider                = "aws-sns"
  rate_limit_per_minute   = 100
  queue_size              = 1000
  retry_attempts          = 3
  enable_ssl              = false
  enable_authentication   = true
}

# SMS Service Monitoring Configuration
# Volume mount configuration for SMS Service
sms_volume_mounts = {
  data = {
    device        = "/dev/xvdf"
    mount_point   = "/data"
    fs_type       = "xfs"
    mount_options = ["defaults", "nofail"]
  },
  logs = {
    device        = "/dev/xvdg"
    mount_point   = "/var/log/sms"
    fs_type       = "xfs"
    mount_options = ["defaults", "nofail"]
  },
  backup = {
    device        = "/dev/xvdh"
    mount_point   = "/backup"
    fs_type       = "xfs"
    mount_options = ["defaults", "nofail"]
  }
}

sms_cloudwatch_log_group = "/aws/ec2/sms-service"
sms_enable_monitoring = true
sms_log_retention_days = 7

# =============================================================================
# DAM GATEWAY AZ1 CONFIGURATION (dam-agent-gateway-az1)
# =============================================================================

# DAM Gateway AZ1 Instance Configuration
dam_gateway_az1_name = "iz-vm-a-gen-dam-001"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
dam_gateway_az1_ami_id = "*********************"  # Amazon Linux 2 AMI
dam_gateway_az1_instance_type = "m5.xlarge"  # 4 vCPU, 16GB RAM as per sizing doc
dam_gateway_az1_tags = {
  Name          = "iz-vm-a-gen-dam-001"
  Agency-Code   = "infovault"
  Project-Code  = "iv"
  Zone          = "iz"
  Service       = "vm"
  Az            = "a"
  Environment   = "dev"
  Workloads     = "dam"
  Running-Number = "001"
  CostCenter    = "CC-12345"
  ManagedBy     = "terraform"
}
dam_gateway_az1_key_name = "dam-key"
dam_gateway_az1_security_group_ids = ["sg-0de73e7e87bb698d3"]  # Database security group
dam_gateway_az1_subnet_id = "subnet-0d4f42cf30b20c42d"  # DLZ1 subnet
dam_gateway_az1_availability_zone = "ap-southeast-1a"
dam_gateway_az1_private_ip_address = "**********"  # Static IP assignment
dam_gateway_az1_monitoring_enabled = true
dam_gateway_az1_ebs_optimized = true
dam_gateway_az1_source_dest_check = true
dam_gateway_az1_iam_instance_profile_name = "dev-dam-management-servers-profile"

# DAM Gateway AZ1 Storage Configuration
dam_gateway_az1_root_block_device = {
  volume_type           = "gp3"
  volume_size           = 30
  delete_on_termination = true
  encrypted             = true
}

dam_gateway_az1_ebs_block_devices = [
  {
    device_name           = "/dev/xvdf"
    volume_type           = "gp3"
    volume_size           = 50
    delete_on_termination = true
    encrypted             = true
  }
]

# DAM Gateway AZ1 Configuration Parameters
dam_gateway_az1_config = {
  gateway_port            = 9090
  management_port         = 9091
  agent_port              = 8443
  database_type           = "sqlserver"
  monitoring_enabled      = true
  alert_enabled           = true
  log_retention_days      = 30
  enable_ssl              = true
  enable_authentication   = true
}

# DAM Gateway AZ1 Monitoring Configuration
dam_gateway_az1_cloudwatch_log_group = "/aws/ec2/dam-gateway-az1"
dam_gateway_az1_enable_monitoring = true
dam_gateway_az1_log_retention_days = 30

# =============================================================================
# DAM GATEWAY AZ2 CONFIGURATION (dam-agent-gateway-az2)
# =============================================================================

# DAM Gateway AZ2 Instance Configuration
dam_gateway_az2_name = "iz-vm-b-gen-dam-002"  # Following naming convention: [Zone]-[Service]-[Az]-[Env]-[workload]-[Running Number]
dam_gateway_az2_ami_id = "*********************"  # Amazon Linux 2 AMI
dam_gateway_az2_instance_type = "m5.xlarge"  # 4 vCPU, 16GB RAM as per sizing doc
dam_gateway_az2_tags = {
  Name          = "iz-vm-b-gen-dam-002"
  Agency-Code   = "infovault"
  Project-Code  = "iv"
  Zone          = "iz"
  Service       = "vm"
  Az            = "b"
  Environment   = "dev"
  Workloads     = "dam"
  Running-Number = "002"
  CostCenter    = "CC-12345"
  ManagedBy     = "terraform"
}
dam_gateway_az2_key_name = "dam-key"
dam_gateway_az2_security_group_ids = ["sg-0de73e7e87bb698d3"]  # Database security group
dam_gateway_az2_subnet_id = "subnet-0c775a4f57dfa0e61"  # DLZ2 subnet
dam_gateway_az2_availability_zone = "ap-southeast-1b"
dam_gateway_az2_private_ip_address = "**********"  # Static IP assignment
dam_gateway_az2_monitoring_enabled = true
dam_gateway_az2_ebs_optimized = true
dam_gateway_az2_source_dest_check = true
dam_gateway_az2_iam_instance_profile_name = "dev-dam-management-servers-profile"

# DAM Gateway AZ2 Storage Configuration
dam_gateway_az2_root_block_device = {
  volume_type           = "gp3"
  volume_size           = 30
  delete_on_termination = true
  encrypted             = true
}

dam_gateway_az2_ebs_block_devices = [
  {
    device_name           = "/dev/xvdf"
    volume_type           = "gp3"
    volume_size           = 50
    delete_on_termination = true
    encrypted             = true
  }
]

# DAM Gateway AZ2 Configuration Parameters
dam_gateway_az2_config = {
  gateway_port            = 9090
  management_port         = 9091
  agent_port              = 8443
  database_type           = "sqlserver"
  monitoring_enabled      = true
  alert_enabled           = true
  log_retention_days      = 30
  enable_ssl              = true
  enable_authentication   = true
}

# DAM Gateway AZ2 Monitoring Configuration
dam_gateway_az2_cloudwatch_log_group = "/aws/ec2/dam-gateway-az2"
dam_gateway_az2_enable_monitoring = true
dam_gateway_az2_log_retention_days = 30

