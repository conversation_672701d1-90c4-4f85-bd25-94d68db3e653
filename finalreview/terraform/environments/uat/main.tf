# Internet Compartment Module Configurations

# Internet Compartment Compute Resources
module "internet_compartment" {
  source = "../../stacks/compute/internet_compartment"

  # SMTP Relay Configuration
  smtp_relay = var.smtp_relay_config

  # Squid Proxy Configuration
  squid_proxy = var.squid_proxy

  # CAM Ingress Configuration
  cam_ingress = var.cam_ingress

  # Common Tags
  common_tags = merge(var.common_tags, {
    Agency-Code   = "infovault"
    Project-Code  = "iv"
    Zone          = "ez"
    Service       = "vm"
    Az            = "a"
    Environment   = "dev"
    CostCenter    = "CC-12345"
    ManagedBy     = "terraform"
  })
}

# InfoVault DEV Environment Infrastructure
# Use Case 4: Setup / Manage AWS Infrastructure (DEV)

# Local values for resource naming and configuration
locals {
  name_prefix = "${var.project_name}-${var.environment}"

  # Merge common tags with environment-specific tags
  common_tags = merge(var.common_tags, {
    Environment = var.environment
    Region      = var.aws_region
    Terraform   = "true"
  })
}

# =============================================================================
# NETWORKING INFRASTRUCTURE
# =============================================================================

# Intranet Management Compartment VPC
module "intranet_management" {
  source = "../../stacks/networking/intranet_management"

  # VPC Configuration
  vpc_cidr                             = var.intranet_management.vpc_cidr
  vpc_name                             = var.intranet_management.vpc_name
  enable_dns_hostnames                 = var.intranet_management.enable_dns_hostnames
  enable_dns_support                   = var.intranet_management.enable_dns_support
  enable_network_address_usage_metrics = var.intranet_management.enable_network_address_usage_metrics
  instance_tenancy                     = var.intranet_management.instance_tenancy
  create_internet_gateway              = var.intranet_management.create_internet_gateway

  # Subnets
  public_subnets  = var.intranet_management.public_subnets
  private_subnets = var.intranet_management.private_subnets

  # NAT Gateways
  nat_gateways = var.intranet_management.nat_gateways

  # Route Tables
  public_route_tables  = var.intranet_management.public_route_tables
  private_route_tables = var.intranet_management.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.intranet_management.public_subnet_route_associations
  private_subnet_route_associations = var.intranet_management.private_subnet_route_associations

  # Routes
  private_nat_routes = var.intranet_management.private_nat_routes
  vpc_peering_routes = var.intranet_management.vpc_peering_routes

  # Security Groups
  security_groups              = var.intranet_management.security_groups
  security_group_ingress_rules = var.intranet_management.security_group_ingress_rules
  security_group_egress_rules  = var.intranet_management.security_group_egress_rules

  # Network ACLs
  network_acls              = var.intranet_management.network_acls
  network_acl_ingress_rules = var.intranet_management.network_acl_ingress_rules
  network_acl_egress_rules  = var.intranet_management.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.intranet_management.vpc_endpoints

  # Common Tags
  common_tags = local.common_tags
}

# Internet Facing Compartment VPC
module "internet_facing" {
  source = "../../stacks/networking/internet_facing"

  # VPC Configuration
  vpc_cidr                             = var.internet_facing.vpc_cidr
  vpc_name                             = var.internet_facing.vpc_name
  enable_dns_hostnames                 = var.internet_facing.enable_dns_hostnames
  enable_dns_support                   = var.internet_facing.enable_dns_support
  enable_network_address_usage_metrics = var.internet_facing.enable_network_address_usage_metrics
  instance_tenancy                     = var.internet_facing.instance_tenancy
  create_internet_gateway              = var.internet_facing.create_internet_gateway

  # Subnets
  public_subnets  = var.internet_facing.public_subnets
  private_subnets = var.internet_facing.private_subnets

  # NAT Gateways
  nat_gateways = var.internet_facing.nat_gateways

  # Route Tables
  public_route_tables  = var.internet_facing.public_route_tables
  private_route_tables = var.internet_facing.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.internet_facing.public_subnet_route_associations
  private_subnet_route_associations = var.internet_facing.private_subnet_route_associations

  # Routes
  private_nat_routes = var.internet_facing.private_nat_routes
  vpc_peering_routes = var.internet_facing.vpc_peering_routes

  # Security Groups
  security_groups              = var.internet_facing.security_groups
  security_group_ingress_rules = var.internet_facing.security_group_ingress_rules
  security_group_egress_rules  = var.internet_facing.security_group_egress_rules

  # Network ACLs
  network_acls              = var.internet_facing.network_acls
  network_acl_ingress_rules = var.internet_facing.network_acl_ingress_rules
  network_acl_egress_rules  = var.internet_facing.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.internet_facing.vpc_endpoints

  # Common Tags
  common_tags = local.common_tags
}

# Gen Facing Compartment VPC
module "gen_facing" {
  source = "../../stacks/networking/gen_facing"

  # VPC Configuration
  vpc_cidr                             = var.gen_facing.vpc_cidr
  vpc_name                             = var.gen_facing.vpc_name
  enable_dns_hostnames                 = var.gen_facing.enable_dns_hostnames
  enable_dns_support                   = var.gen_facing.enable_dns_support
  enable_network_address_usage_metrics = var.gen_facing.enable_network_address_usage_metrics
  instance_tenancy                     = var.gen_facing.instance_tenancy
  create_internet_gateway              = var.gen_facing.create_internet_gateway

  # Subnets
  public_subnets  = var.gen_facing.public_subnets
  private_subnets = var.gen_facing.private_subnets

  # NAT Gateways
  nat_gateways = var.gen_facing.nat_gateways

  # Route Tables
  public_route_tables  = var.gen_facing.public_route_tables
  private_route_tables = var.gen_facing.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.gen_facing.public_subnet_route_associations
  private_subnet_route_associations = var.gen_facing.private_subnet_route_associations

  # Routes
  private_nat_routes = var.gen_facing.private_nat_routes
  vpc_peering_routes = var.gen_facing.vpc_peering_routes

  # Security Groups
  security_groups              = var.gen_facing.security_groups
  security_group_ingress_rules = var.gen_facing.security_group_ingress_rules
  security_group_egress_rules  = var.gen_facing.security_group_egress_rules

  # Network ACLs
  network_acls              = var.gen_facing.network_acls
  network_acl_ingress_rules = var.gen_facing.network_acl_ingress_rules
  network_acl_egress_rules  = var.gen_facing.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.gen_facing.vpc_endpoints

  # Common Tags
  common_tags = local.common_tags
}

# ABLRHF Compartment VPC
module "ablrhf" {
  source = "../../stacks/networking/ablrhf"

  # VPC Configuration
  vpc_cidr                             = var.ablrhf.vpc_cidr
  vpc_name                             = var.ablrhf.vpc_name
  enable_dns_hostnames                 = var.ablrhf.enable_dns_hostnames
  enable_dns_support                   = var.ablrhf.enable_dns_support
  enable_network_address_usage_metrics = var.ablrhf.enable_network_address_usage_metrics
  instance_tenancy                     = var.ablrhf.instance_tenancy
  create_internet_gateway              = var.ablrhf.create_internet_gateway

  # Subnets
  public_subnets  = var.ablrhf.public_subnets
  private_subnets = var.ablrhf.private_subnets

  # NAT Gateways
  nat_gateways = var.ablrhf.nat_gateways

  # Route Tables
  public_route_tables  = var.ablrhf.public_route_tables
  private_route_tables = var.ablrhf.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.ablrhf.public_subnet_route_associations
  private_subnet_route_associations = var.ablrhf.private_subnet_route_associations

  # Routes
  private_nat_routes = var.ablrhf.private_nat_routes
  vpc_peering_routes = var.ablrhf.vpc_peering_routes

  # Security Groups
  security_groups              = var.ablrhf.security_groups
  security_group_ingress_rules = var.ablrhf.security_group_ingress_rules
  security_group_egress_rules  = var.ablrhf.security_group_egress_rules

  # Network ACLs
  network_acls              = var.ablrhf.network_acls
  network_acl_ingress_rules = var.ablrhf.network_acl_ingress_rules
  network_acl_egress_rules  = var.ablrhf.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.ablrhf.vpc_endpoints

  # Common Tags
  common_tags = local.common_tags
}

# Patching Compartment VPC
module "patching" {
  source = "../../stacks/networking/patching"

  # VPC Configuration
  vpc_cidr                             = var.patching.vpc_cidr
  vpc_name                             = var.patching.vpc_name
  enable_dns_hostnames                 = var.patching.enable_dns_hostnames
  enable_dns_support                   = var.patching.enable_dns_support
  enable_network_address_usage_metrics = var.patching.enable_network_address_usage_metrics
  instance_tenancy                     = var.patching.instance_tenancy
  create_internet_gateway              = var.patching.create_internet_gateway

  # Subnets
  public_subnets  = var.patching.public_subnets
  private_subnets = var.patching.private_subnets

  # NAT Gateways
  nat_gateways = var.patching.nat_gateways

  # Route Tables
  public_route_tables  = var.patching.public_route_tables
  private_route_tables = var.patching.private_route_tables

  # Route Table Associations
  public_subnet_route_associations  = var.patching.public_subnet_route_associations
  private_subnet_route_associations = var.patching.private_subnet_route_associations

  # Routes
  private_nat_routes = var.patching.private_nat_routes
  vpc_peering_routes = var.patching.vpc_peering_routes

  # Security Groups
  security_groups              = var.patching.security_groups
  security_group_ingress_rules = var.patching.security_group_ingress_rules
  security_group_egress_rules  = var.patching.security_group_egress_rules

  # Network ACLs
  network_acls              = var.patching.network_acls
  network_acl_ingress_rules = var.patching.network_acl_ingress_rules
  network_acl_egress_rules  = var.patching.network_acl_egress_rules

  # VPC Endpoints
  vpc_endpoints = var.patching.vpc_endpoints

  # Common Tags
  common_tags = local.common_tags
}

# =============================================================================
# STORAGE INFRASTRUCTURE
# =============================================================================

# S3 Bucket for InfoVault Application Data
module "app_data_bucket" {
  source = "../../modules/s3"

  # Bucket Configuration
  bucket_name    = var.s3_bucket_name
  name_prefix    = "${local.name_prefix}-app-data"
  bucket_purpose = "InfoVault Application Data Storage"
  force_destroy  = var.s3_force_destroy

  # Versioning
  versioning_enabled = var.enable_s3_versioning

  # Encryption
  encryption_algorithm = var.s3_encryption_algorithm
  kms_key_id           = var.s3_kms_key_id
  bucket_key_enabled   = var.s3_bucket_key_enabled

  # Public Access Block (Security)
  block_public_acls       = var.s3_block_public_acls
  block_public_policy     = var.s3_block_public_policy
  ignore_public_acls      = var.s3_ignore_public_acls
  restrict_public_buckets = var.s3_restrict_public_buckets

  # Lifecycle Management
  lifecycle_enabled          = var.s3_lifecycle_enabled
  transition_to_ia_days      = var.s3_transition_to_ia_days
  transition_to_glacier_days = var.s3_transition_to_glacier_days
  expiration_days            = var.s3_expiration_days
  multipart_upload_days      = var.s3_multipart_upload_days

  # Logging
  logging_enabled       = var.s3_logging_enabled
  logging_target_bucket = var.s3_logging_target_bucket
  logging_target_prefix = var.s3_logging_target_prefix

  # Notifications
  notification_enabled = var.s3_notification_enabled

  # Tags
  common_tags = local.common_tags
}

# =============================================================================
# COMPUTE INFRASTRUCTURE - EKS
# =============================================================================

# EKS Cluster for InfoVault Application
module "eks_cluster" {
  source = "../../stacks/compute/eks"

  environment = var.environment
  region      = var.aws_region
  common_tags = local.common_tags

  # EKS Configuration
  cluster_name               = var.cluster_name
  cluster_version            = var.cluster_version
  cluster_role_name          = var.cluster_role_name
  node_group_role_name       = var.node_group_role_name
  subnet_ids                 = var.subnet_ids
  cluster_security_group_ids = var.cluster_security_group_ids
  endpoint_private_access    = var.endpoint_private_access
  endpoint_public_access     = var.endpoint_public_access
  public_access_cidrs        = var.public_access_cidrs
  cluster_log_types          = var.cluster_log_types
  kms_key_arn                = var.kms_key_arn

  # Node Group Configuration
  node_group_name           = var.node_group_name
  node_group_subnet_ids     = var.node_group_subnet_ids
  node_group_instance_types = var.node_group_instance_types
  node_group_ami_type       = var.node_group_ami_type
  node_group_capacity_type  = var.node_group_capacity_type
  node_group_disk_size      = var.node_group_disk_size
  node_group_desired_size   = var.node_group_desired_size
  node_group_max_size       = var.node_group_max_size
  node_group_min_size       = var.node_group_min_size
  node_group_labels         = var.node_group_labels

  # EKS Addon Versions
  addon_amazon_cloudwatch_observability_version = var.addon_amazon_cloudwatch_observability_version
  addon_aws_ebs_csi_driver_version              = var.addon_aws_ebs_csi_driver_version
  addon_aws_guardduty_agent_version             = var.addon_aws_guardduty_agent_version
  addon_coredns_version                         = var.addon_coredns_version
  addon_eks_pod_identity_agent_version          = var.addon_eks_pod_identity_agent_version
  addon_kube_proxy_version                      = var.addon_kube_proxy_version
  addon_vpc_cni_version                         = var.addon_vpc_cni_version
}

# =============================================================================
# EKS VPC ENDPOINTS
# =============================================================================

# VPC Endpoints for EKS in Gen-Facing Compartment
module "eks_vpc_endpoints" {
  source = "../../modules/networking/vpc_endpoints_eks"

  vpc_id             = module.gen_facing.vpc_id
  vpc_cidr           = var.gen_facing.vpc_cidr
  subnet_ids         = var.eks_vpc_endpoints.subnet_ids
  ssm_subnet_ids     = var.eks_vpc_endpoints.ssm_subnet_ids
  route_table_ids    = var.eks_vpc_endpoints.route_table_ids
  aws_region         = var.aws_region
  name_prefix        = "${local.name_prefix}-gen-facing"
  
  # Enable specific endpoints
  enable_ec2_endpoint       = var.eks_vpc_endpoints.enable_ec2_endpoint
  enable_ecr_endpoints      = var.eks_vpc_endpoints.enable_ecr_endpoints
  enable_eks_endpoint       = var.eks_vpc_endpoints.enable_eks_endpoint
  enable_s3_endpoint        = var.eks_vpc_endpoints.enable_s3_endpoint
  enable_ssm_endpoints      = var.eks_vpc_endpoints.enable_ssm_endpoints
  enable_guardduty_endpoint = var.eks_vpc_endpoints.enable_guardduty_endpoint

  common_tags = local.common_tags
}

# =============================================================================
# IAM ROLES FOR SERVICE ACCOUNTS (IRSA)
# =============================================================================

# IRSA for Kubernetes Controllers
module "irsa" {
  source = "../../modules/iam/irsa"

  cluster_oidc_issuer_url                   = module.eks_cluster.cluster_oidc_issuer_url
  aws_region                                = var.aws_region
  aws_load_balancer_controller_role_name    = var.irsa.aws_load_balancer_controller_role_name
  cert_manager_role_name                    = var.irsa.cert_manager_role_name
  cluster_autoscaler_role_name              = var.irsa.cluster_autoscaler_role_name
  external_dns_role_name                    = var.irsa.external_dns_role_name

  common_tags = local.common_tags
}

# =============================================================================
# CUSTOM IAM POLICIES FOR EKS NODES
# =============================================================================

# Custom IAM Policies for EKS Node Groups
module "eks_node_policies" {
  source = "../../modules/iam/eks_node_policies"

  s3_access_policy_name        = var.eks_node_policies.s3_access_policy_name
  kms_access_policy_name       = var.eks_node_policies.kms_access_policy_name
  node_group_role_name         = module.eks_cluster.node_group_iam_role_name
  attach_to_node_group_role    = var.eks_node_policies.attach_to_node_group_role
  attach_additional_policies   = var.eks_node_policies.attach_additional_policies

  common_tags = local.common_tags
}

# =============================================================================
# KUBERNETES CONTROLLERS
# =============================================================================

# Kubernetes Controllers Stack
module "kubernetes_controllers" {
  source = "../../stacks/kubernetes/controllers"

  cluster_name                               = var.cluster_name
  cluster_endpoint                           = module.eks_cluster.cluster_endpoint
  aws_region                                 = var.aws_region
  vpc_id                                     = module.gen_facing.vpc_id
  
  # Controller Configuration
  enable_aws_load_balancer_controller        = var.kubernetes_controllers.enable_aws_load_balancer_controller
  aws_load_balancer_controller_version       = var.kubernetes_controllers.aws_load_balancer_controller_version
  aws_load_balancer_controller_role_arn      = module.irsa.aws_load_balancer_controller_role_arn
  
  enable_cert_manager                        = var.kubernetes_controllers.enable_cert_manager
  cert_manager_version                       = var.kubernetes_controllers.cert_manager_version
  enable_cert_manager_pca_issuer             = var.kubernetes_controllers.enable_cert_manager_pca_issuer
  cert_manager_pca_issuer_version            = var.kubernetes_controllers.cert_manager_pca_issuer_version
  cert_manager_role_arn                      = module.irsa.cert_manager_role_arn
  
  enable_cluster_autoscaler                  = var.kubernetes_controllers.enable_cluster_autoscaler
  cluster_autoscaler_version                 = var.kubernetes_controllers.cluster_autoscaler_version
  cluster_autoscaler_role_arn                = module.irsa.cluster_autoscaler_role_arn
  
  enable_external_dns                        = var.kubernetes_controllers.enable_external_dns
  external_dns_version                       = var.kubernetes_controllers.external_dns_version
  external_dns_role_arn                      = module.irsa.external_dns_role_arn

  common_tags = local.common_tags
}

# =============================================================================
# COMPUTE INFRASTRUCTURE - EC2
# =============================================================================

# NFS Server
module "nfs_server" {
  source = "../../modules/compute/ec2_instance"

  instance_name             = var.nfs_server_name
  ami_id                    = var.nfs_server_ami_id
  instance_type             = var.nfs_server_instance_type
  subnet_id                 = var.nfs_server_subnet_id
  security_group_ids        = var.nfs_server_security_group_ids
  key_name                  = var.nfs_server_key_name
  iam_instance_profile_name = var.nfs_server_iam_instance_profile_name
  private_ip_address        = var.nfs_server_private_ip_address
  monitoring_enabled        = var.nfs_server_monitoring_enabled
  ebs_optimized             = var.nfs_server_ebs_optimized
  source_dest_check         = var.nfs_server_source_dest_check
  root_block_device         = var.nfs_server_root_block_device
  ebs_block_devices         = var.nfs_server_ebs_block_devices
  prevent_destroy           = var.environment == "prod"

  common_tags = merge(var.common_tags, {
    Component = "Storage"
    Service   = "NFS"
  })
}

# Migration Server
module "migration_server" {
  source = "../../modules/compute/ec2_instance"

  instance_name             = var.migration_server_name
  ami_id                    = var.migration_server_ami_id
  instance_type             = var.migration_server_instance_type
  subnet_id                 = var.migration_server_subnet_id
  security_group_ids        = var.migration_server_security_group_ids
  key_name                  = var.migration_server_key_name
  iam_instance_profile_name = var.migration_server_iam_instance_profile_name
  private_ip_address        = var.migration_server_private_ip_address
  monitoring_enabled        = var.migration_server_monitoring_enabled
  ebs_optimized             = var.migration_server_ebs_optimized
  source_dest_check         = var.migration_server_source_dest_check
  root_block_device         = var.migration_server_root_block_device
  ebs_block_devices         = var.migration_server_ebs_block_devices
  prevent_destroy           = var.environment == "prod"

  common_tags = merge(var.common_tags, {
    Component = "Migration"
    Service   = "Data Transfer"
  })
}

# Internet-facing Compute Instances (Squid Proxy and Bastion Host)
module "internet_facing_compute" {
  source = "../../stacks/compute/internet_facing_compute"

  squid_proxy  = var.squid_proxy
  bastion_host = var.bastion_host
  common_tags  = local.common_tags
}

# Patching Workloads (WSUS Server and RHEL Repository)
module "patching_workloads" {
  source = "../../stacks/compute/patching_workloads"

# SMS Service and Storage Configuration

# SMS Service EBS Volumes
module "sms_volumes" {
  source = "../../modules/storage/ebs_volumes"

  name_prefix       = "infovault-${var.environment}-sms"
  availability_zone = var.sms_service_availability_zone
  instance_id       = module.sms_service.instance_id
  volumes           = var.sms_service_ebs_volumes
  common_tags       = var.common_tags

  # Volume management settings
  prevent_destroy                 = var.environment == "prod"
  force_detach                    = true
  stop_instance_before_detaching  = false
  skip_destroy_on_attachment      = true
}
  wsus_instance_name        = var.wsus_instance_name
  wsus_ami_id              = var.wsus_ami_id
  wsus_instance_type       = var.wsus_instance_type
  wsus_subnet_id           = var.wsus_subnet_id
  wsus_security_group_ids  = var.wsus_security_group_ids
  wsus_private_ip          = var.wsus_private_ip
  wsus_iam_instance_profile = var.wsus_iam_instance_profile
  
  # WSUS Configuration Parameters
  wsus_port                     = var.wsus_port
  wsus_ssl_port                = var.wsus_ssl_port
  wsus_content_dir             = var.wsus_content_dir
  wsus_database_name           = var.wsus_database_name
  wsus_update_languages        = var.wsus_update_languages
  wsus_update_classifications  = var.wsus_update_classifications
  wsus_products               = var.wsus_products
  
  # WSUS Admin Configuration
  wsus_admin_users         = var.wsus_admin_users
  wsus_admin_passwords     = var.wsus_admin_passwords
  wsus_domain_join_enabled = var.wsus_domain_join_enabled
  wsus_domain_name         = var.wsus_domain_name
  wsus_domain_user         = var.wsus_domain_user
  wsus_domain_password     = var.wsus_domain_password
  
  # WSUS Monitoring
  wsus_cloudwatch_log_group = var.wsus_cloudwatch_log_group
  wsus_enable_monitoring    = var.wsus_enable_monitoring
  
  # RHEL Repository Configuration
  rhel_instance_name        = var.rhel_instance_name
  rhel_ami_id              = var.rhel_ami_id
  rhel_instance_type       = var.rhel_instance_type
  rhel_subnet_id           = var.rhel_subnet_id
  rhel_security_group_ids  = var.rhel_security_group_ids
  rhel_private_ip          = var.rhel_private_ip
  rhel_iam_instance_profile = var.rhel_iam_instance_profile
  
  # Repository Configuration Parameters
  rhel_repository_port          = var.rhel_repository_port
  rhel_repository_root          = var.rhel_repository_root
  rhel_repository_distributions = var.rhel_repository_distributions
  rhel_repository_architectures = var.rhel_repository_architectures
  
  # Upstream Repository Sync
  rhel_upstream_repositories = var.rhel_upstream_repositories
  rhel_sync_schedule        = var.rhel_sync_schedule
  
  # RHEL Admin Configuration
  rhel_admin_users         = var.rhel_admin_users
  rhel_ssh_public_keys     = var.rhel_ssh_public_keys
  
  # RHEL Monitoring
  rhel_cloudwatch_log_group = var.rhel_cloudwatch_log_group
  rhel_enable_monitoring    = var.rhel_enable_monitoring
  
  common_tags = local.common_tags
}

# =============================================================================
# MANAGEMENT COMPUTE INFRASTRUCTURE
# =============================================================================

# Management EC2 Instances Stack
module "management_ec2" {
  source = "../../stacks/compute/management_ec2"

  # Required base variables
  common_tags = merge(local.common_tags, {
    Component = "Management"
    Tier      = "Management"
  })

  # GitLab Runner Configuration
  infovault_dev_gitlab_runner_name                               = var.infovault_dev_gitlab_runner_name
  infovault_dev_gitlab_runner_ami_id                             = var.infovault_dev_gitlab_runner_ami_id
  infovault_dev_gitlab_runner_instance_type                      = var.infovault_dev_gitlab_runner_instance_type
  infovault_dev_gitlab_runner_key_name                           = var.infovault_dev_gitlab_runner_key_name
  infovault_dev_gitlab_runner_security_group_ids                 = var.infovault_dev_gitlab_runner_security_group_ids
  infovault_dev_gitlab_runner_subnet_id                          = var.infovault_dev_gitlab_runner_subnet_id
  infovault_dev_gitlab_runner_iam_instance_profile_name          = var.infovault_dev_gitlab_runner_iam_instance_profile_name
  infovault_dev_gitlab_runner_availability_zone                  = var.infovault_dev_gitlab_runner_availability_zone
  infovault_dev_gitlab_runner_monitoring_enabled                 = var.infovault_dev_gitlab_runner_monitoring_enabled
  infovault_dev_gitlab_runner_ebs_optimized                      = var.infovault_dev_gitlab_runner_ebs_optimized
  infovault_dev_gitlab_runner_source_dest_check                  = var.infovault_dev_gitlab_runner_source_dest_check
  infovault_dev_gitlab_runner_private_ip_address                 = var.infovault_dev_gitlab_runner_private_ip_address
  infovault_dev_gitlab_runner_root_block_device                  = var.infovault_dev_gitlab_runner_root_block_device
  infovault_dev_gitlab_runner_ebs_block_devices                  = var.infovault_dev_gitlab_runner_ebs_block_devices
  infovault_dev_linux_tooling_server_name                        = var.infovault_dev_linux_tooling_server_name
  infovault_dev_linux_tooling_server_ami_id                      = var.infovault_dev_linux_tooling_server_ami_id
  infovault_dev_linux_tooling_server_instance_type               = var.infovault_dev_linux_tooling_server_instance_type
  infovault_dev_linux_tooling_server_key_name                    = var.infovault_dev_linux_tooling_server_key_name
  infovault_dev_linux_tooling_server_security_group_ids          = var.infovault_dev_linux_tooling_server_security_group_ids
  infovault_dev_linux_tooling_server_subnet_id                   = var.infovault_dev_linux_tooling_server_subnet_id
  infovault_dev_linux_tooling_server_iam_instance_profile_name   = var.infovault_dev_linux_tooling_server_iam_instance_profile_name
  infovault_dev_linux_tooling_server_availability_zone           = var.infovault_dev_linux_tooling_server_availability_zone
  infovault_dev_linux_tooling_server_monitoring_enabled          = var.infovault_dev_linux_tooling_server_monitoring_enabled
  infovault_dev_linux_tooling_server_ebs_optimized               = var.infovault_dev_linux_tooling_server_ebs_optimized
  infovault_dev_linux_tooling_server_source_dest_check           = var.infovault_dev_linux_tooling_server_source_dest_check
  infovault_dev_linux_tooling_server_private_ip_address          = var.infovault_dev_linux_tooling_server_private_ip_address
  infovault_dev_linux_tooling_server_root_block_device           = var.infovault_dev_linux_tooling_server_root_block_device
  infovault_dev_linux_tooling_server_ebs_block_devices           = var.infovault_dev_linux_tooling_server_ebs_block_devices
  mgmt_newgenadm_win_tooling_01_name                             = var.mgmt_newgenadm_win_tooling_01_name
  mgmt_newgenadm_win_tooling_01_ami_id                           = var.mgmt_newgenadm_win_tooling_01_ami_id
  mgmt_newgenadm_win_tooling_01_instance_type                    = var.mgmt_newgenadm_win_tooling_01_instance_type
  mgmt_newgenadm_win_tooling_01_key_name                         = var.mgmt_newgenadm_win_tooling_01_key_name
  mgmt_newgenadm_win_tooling_01_security_group_ids               = var.mgmt_newgenadm_win_tooling_01_security_group_ids
  mgmt_newgenadm_win_tooling_01_subnet_id                        = var.mgmt_newgenadm_win_tooling_01_subnet_id
  mgmt_newgenadm_win_tooling_01_iam_instance_profile_name        = var.mgmt_newgenadm_win_tooling_01_iam_instance_profile_name
  mgmt_newgenadm_win_tooling_01_availability_zone                = var.mgmt_newgenadm_win_tooling_01_availability_zone
  mgmt_newgenadm_win_tooling_01_monitoring_enabled               = var.mgmt_newgenadm_win_tooling_01_monitoring_enabled
  mgmt_newgenadm_win_tooling_01_ebs_optimized                    = var.mgmt_newgenadm_win_tooling_01_ebs_optimized
  mgmt_newgenadm_win_tooling_01_source_dest_check                = var.mgmt_newgenadm_win_tooling_01_source_dest_check
  mgmt_newgenadm_win_tooling_01_private_ip_address               = var.mgmt_newgenadm_win_tooling_01_private_ip_address
  mgmt_newgenadm_win_tooling_01_root_block_device                = var.mgmt_newgenadm_win_tooling_01_root_block_device
  mgmt_newgenadm_win_tooling_01_ebs_block_devices                = var.mgmt_newgenadm_win_tooling_01_ebs_block_devices
  dev_management_server_avm150_new_name                          = var.dev_management_server_avm150_new_name
  dev_management_server_avm150_new_ami_id                        = var.dev_management_server_avm150_new_ami_id
  dev_management_server_avm150_new_instance_type                 = var.dev_management_server_avm150_new_instance_type
  dev_management_server_avm150_new_key_name                      = var.dev_management_server_avm150_new_key_name
  dev_management_server_avm150_new_security_group_ids            = var.dev_management_server_avm150_new_security_group_ids
  dev_management_server_avm150_new_subnet_id                     = var.dev_management_server_avm150_new_subnet_id
  dev_management_server_avm150_new_iam_instance_profile_name     = var.dev_management_server_avm150_new_iam_instance_profile_name
  dev_management_server_avm150_new_availability_zone             = var.dev_management_server_avm150_new_availability_zone
  dev_management_server_avm150_new_monitoring_enabled            = var.dev_management_server_avm150_new_monitoring_enabled
  dev_management_server_avm150_new_ebs_optimized                 = var.dev_management_server_avm150_new_ebs_optimized
  dev_management_server_avm150_new_source_dest_check             = var.dev_management_server_avm150_new_source_dest_check
  dev_management_server_avm150_new_private_ip_address            = var.dev_management_server_avm150_new_private_ip_address
  dev_management_server_avm150_new_root_block_device             = var.dev_management_server_avm150_new_root_block_device
  dev_management_server_avm150_new_ebs_block_devices             = var.dev_management_server_avm150_new_ebs_block_devices
  dev_dra_admin_server_new_name                                  = var.dev_dra_admin_server_new_name
  dev_dra_admin_server_new_ami_id                                = var.dev_dra_admin_server_new_ami_id
  dev_dra_admin_server_new_instance_type                         = var.dev_dra_admin_server_new_instance_type
  dev_dra_admin_server_new_key_name                              = var.dev_dra_admin_server_new_key_name
  dev_dra_admin_server_new_security_group_ids                    = var.dev_dra_admin_server_new_security_group_ids
  dev_dra_admin_server_new_subnet_id                             = var.dev_dra_admin_server_new_subnet_id
  dev_dra_admin_server_new_iam_instance_profile_name             = var.dev_dra_admin_server_new_iam_instance_profile_name
  dev_dra_admin_server_new_availability_zone                     = var.dev_dra_admin_server_new_availability_zone
  dev_dra_admin_server_new_monitoring_enabled                    = var.dev_dra_admin_server_new_monitoring_enabled
  dev_dra_admin_server_new_ebs_optimized                         = var.dev_dra_admin_server_new_ebs_optimized
  dev_dra_admin_server_new_source_dest_check                     = var.dev_dra_admin_server_new_source_dest_check
  dev_dra_admin_server_new_private_ip_address                    = var.dev_dra_admin_server_new_private_ip_address
  dev_dra_admin_server_new_root_block_device                     = var.dev_dra_admin_server_new_root_block_device
  dev_dra_admin_server_new_ebs_block_devices                     = var.dev_dra_admin_server_new_ebs_block_devices
  dev_dra_analytics_server_new_name                              = var.dev_dra_analytics_server_new_name
  dev_dra_analytics_server_new_ami_id                            = var.dev_dra_analytics_server_new_ami_id
  dev_dra_analytics_server_new_instance_type                     = var.dev_dra_analytics_server_new_instance_type
  dev_dra_analytics_server_new_key_name                          = var.dev_dra_analytics_server_new_key_name
  dev_dra_analytics_server_new_security_group_ids                = var.dev_dra_analytics_server_new_security_group_ids
  dev_dra_analytics_server_new_subnet_id                         = var.dev_dra_analytics_server_new_subnet_id
  dev_dra_analytics_server_new_iam_instance_profile_name         = var.dev_dra_analytics_server_new_iam_instance_profile_name
  dev_dra_analytics_server_new_availability_zone                 = var.dev_dra_analytics_server_new_availability_zone
  dev_dra_analytics_server_new_monitoring_enabled                = var.dev_dra_analytics_server_new_monitoring_enabled
  dev_dra_analytics_server_new_ebs_optimized                     = var.dev_dra_analytics_server_new_ebs_optimized
  dev_dra_analytics_server_new_source_dest_check                 = var.dev_dra_analytics_server_new_source_dest_check
  dev_dra_analytics_server_new_private_ip_address                = var.dev_dra_analytics_server_new_private_ip_address
  dev_dra_analytics_server_new_root_block_device                 = var.dev_dra_analytics_server_new_root_block_device
  dev_dra_analytics_server_new_ebs_block_devices                 = var.dev_dra_analytics_server_new_ebs_block_devices
  dev_management_server_avm150_imperva_name                      = var.dev_management_server_avm150_imperva_name
  dev_management_server_avm150_imperva_ami_id                    = var.dev_management_server_avm150_imperva_ami_id
  dev_management_server_avm150_imperva_instance_type             = var.dev_management_server_avm150_imperva_instance_type
  dev_management_server_avm150_imperva_key_name                  = var.dev_management_server_avm150_imperva_key_name
  dev_management_server_avm150_imperva_security_group_ids        = var.dev_management_server_avm150_imperva_security_group_ids
  dev_management_server_avm150_imperva_subnet_id                 = var.dev_management_server_avm150_imperva_subnet_id
  dev_management_server_avm150_imperva_iam_instance_profile_name = var.dev_management_server_avm150_imperva_iam_instance_profile_name
  dev_management_server_avm150_imperva_availability_zone         = var.dev_management_server_avm150_imperva_availability_zone
  dev_management_server_avm150_imperva_monitoring_enabled        = var.dev_management_server_avm150_imperva_monitoring_enabled
  dev_management_server_avm150_imperva_ebs_optimized             = var.dev_management_server_avm150_imperva_ebs_optimized
  dev_management_server_avm150_imperva_source_dest_check         = var.dev_management_server_avm150_imperva_source_dest_check
  dev_management_server_avm150_imperva_private_ip_address        = var.dev_management_server_avm150_imperva_private_ip_address
  dev_management_server_avm150_imperva_root_block_device         = var.dev_management_server_avm150_imperva_root_block_device
  dev_management_server_avm150_imperva_ebs_block_devices         = var.dev_management_server_avm150_imperva_ebs_block_devices
  ad_tooling_windows_02_name                                     = var.ad_tooling_windows_02_name
  ad_tooling_windows_02_ami_id                                   = var.ad_tooling_windows_02_ami_id
  ad_tooling_windows_02_instance_type                            = var.ad_tooling_windows_02_instance_type
  ad_tooling_windows_02_key_name                                 = var.ad_tooling_windows_02_key_name
  ad_tooling_windows_02_security_group_ids                       = var.ad_tooling_windows_02_security_group_ids
  ad_tooling_windows_02_subnet_id                                = var.ad_tooling_windows_02_subnet_id
  ad_tooling_windows_02_iam_instance_profile_name                = var.ad_tooling_windows_02_iam_instance_profile_name
  ad_tooling_windows_02_availability_zone                        = var.ad_tooling_windows_02_availability_zone
  ad_tooling_windows_02_monitoring_enabled                       = var.ad_tooling_windows_02_monitoring_enabled
  ad_tooling_windows_02_ebs_optimized                            = var.ad_tooling_windows_02_ebs_optimized
  ad_tooling_windows_02_source_dest_check                        = var.ad_tooling_windows_02_source_dest_check
  ad_tooling_windows_02_private_ip_address                       = var.ad_tooling_windows_02_private_ip_address
  ad_tooling_windows_02_root_block_device                        = var.ad_tooling_windows_02_root_block_device
  ad_tooling_windows_02_ebs_block_devices                        = var.ad_tooling_windows_02_ebs_block_devices
}

# =============================================================================
# KUBERNETES INFRASTRUCTURE
# =============================================================================

# Kubernetes Infrastructure (Namespaces, Storage Classes, Infrastructure Components)
module "kubernetes_infrastructure" {
  source = "../../stacks/kubernetes/infrastructure"

  environment      = var.environment
  aws_region       = var.aws_region
  cluster_name     = module.eks_cluster.cluster_id
  cluster_endpoint = module.eks_cluster.cluster_endpoint

  # Storage Configuration
  create_shared_storage = var.create_shared_storage
  shared_storage_size   = var.shared_storage_size

  # Infrastructure Configuration
  infrastructure_config = var.infrastructure_config

  # ALB Ingress Configuration
  ssl_certificate_arn = "arn:aws:acm:ap-southeast-1:046276255144:certificate/bcffd4ca-5f26-42ec-97cf-11425c7453eb"
  alb_security_groups = ["sg-0950397297ecb203f", "sg-0353836b1b373e27d"]
  alb_subnet_ids      = ["subnet-023cafbedbb31d13e", "subnet-015e4b2acb87091d9"]

  common_tags = local.common_tags

  depends_on = [
    module.kubernetes_controllers  # Ensure controllers are deployed first
  ]
}

# =============================================================================
# DATABASE INFRASTRUCTURE
# =============================================================================

# InfoVault Database Infrastructure (SQL Server Primary and Read Replica)
module "database_infrastructure" {
  source = "../../stacks/database/infovault_databases"

  # Common Configuration
  environment    = var.environment
  region         = var.aws_region
  project_name   = var.project_name
  common_tags    = local.common_tags

  # Network Configuration
  vpc_id                 = var.database_vpc_id
  db_subnet_dlz1_id      = var.database_subnet_dlz1_id
  db_subnet_dlz2_id      = var.database_subnet_dlz2_id
  management_subnet_cidr = var.management_subnet_cidr
  database_port          = var.database_port

  # Security Configuration
  create_application_security_group = var.create_application_security_group
  additional_database_ingress_rules  = var.additional_database_ingress_rules
  database_egress_rules              = var.database_egress_rules
  application_ingress_rules          = var.application_ingress_rules

  # Monitoring and Connection Configuration
  create_monitoring_role    = var.create_monitoring_role
  create_connection_secret  = var.create_connection_secret
  existing_database_endpoint = var.existing_database_endpoint
  database_name             = var.database_name
  database_username         = var.database_username

  # Primary Database Configuration
  create_primary_database             = var.create_primary_database
  primary_db_engine                  = var.primary_db_engine
  primary_db_engine_version          = var.primary_db_engine_version
  primary_db_instance_class          = var.primary_db_instance_class
  primary_db_name                    = var.primary_db_name
  primary_db_master_username         = var.primary_db_master_username
  manage_master_user_password        = var.manage_master_user_password
  primary_db_allocated_storage       = var.primary_db_allocated_storage
  primary_db_max_allocated_storage   = var.primary_db_max_allocated_storage
  primary_db_storage_type            = var.primary_db_storage_type
  primary_db_storage_encrypted       = var.primary_db_storage_encrypted
  primary_db_kms_key_id              = var.primary_db_kms_key_id
  primary_db_publicly_accessible    = var.primary_db_publicly_accessible
  primary_db_multi_az                = var.primary_db_multi_az
  primary_db_backup_retention_period = var.primary_db_backup_retention_period
  primary_db_backup_window           = var.primary_db_backup_window
  primary_db_maintenance_window      = var.primary_db_maintenance_window
  primary_db_deletion_protection     = var.primary_db_deletion_protection
  primary_db_skip_final_snapshot     = var.primary_db_skip_final_snapshot

  # Read Replica Configuration
  create_read_replica               = var.create_read_replica
  replica_db_engine                = var.replica_db_engine
  replica_db_engine_version        = var.replica_db_engine_version
  replica_db_instance_class        = var.replica_db_instance_class
  replica_db_master_username       = var.replica_db_master_username
  replica_db_allocated_storage     = var.replica_db_allocated_storage
  replica_db_max_allocated_storage = var.replica_db_max_allocated_storage
  replica_db_storage_type          = var.replica_db_storage_type
  replica_db_storage_encrypted     = var.replica_db_storage_encrypted
  replica_db_publicly_accessible  = var.replica_db_publicly_accessible
  replica_db_multi_az              = var.replica_db_multi_az
  replica_db_maintenance_window    = var.replica_db_maintenance_window
  replica_db_deletion_protection   = var.replica_db_deletion_protection
  replica_db_skip_final_snapshot   = var.replica_db_skip_final_snapshot

  # Parameter and Option Groups
  create_parameter_group   = var.create_parameter_group
  parameter_group_family   = var.parameter_group_family
  db_parameters           = var.db_parameters
  create_option_group     = var.create_option_group
  major_engine_version    = var.major_engine_version
  db_options              = var.db_options

  # Monitoring Configuration
  monitoring_interval             = var.monitoring_interval
  enabled_cloudwatch_logs_exports = var.enabled_cloudwatch_logs_exports
  performance_insights_enabled    = var.performance_insights_enabled
}

# =============================================================================
# DATABASE SUPPORT SERVICES
# =============================================================================

# Database Support Services (Redis Cache, SMS Service, DAM Gateways)
# Deployed in database subnets DLZ1 and DLZ2 to support database operations
module "database_support_services" {
  source = "../../stacks/compute/database_support_services"

  # Common Configuration
  common_tags = local.common_tags

  # =============================================================================
  # REDIS CACHE CONFIGURATION
  # =============================================================================
  
  redis_cache_name                      = var.redis_cache_name
  redis_cache_ami_id                    = var.redis_cache_ami_id
  redis_cache_instance_type             = var.redis_cache_instance_type
  redis_cache_key_name                  = var.redis_cache_key_name
  redis_cache_security_group_ids        = var.redis_cache_security_group_ids
  redis_cache_subnet_id                 = var.redis_cache_subnet_id
  redis_cache_availability_zone         = var.redis_cache_availability_zone
  redis_cache_private_ip_address        = var.redis_cache_private_ip_address
  redis_cache_monitoring_enabled        = var.redis_cache_monitoring_enabled
  redis_cache_ebs_optimized             = var.redis_cache_ebs_optimized
  redis_cache_source_dest_check         = var.redis_cache_source_dest_check
  redis_cache_iam_instance_profile_name = var.redis_cache_iam_instance_profile_name
  redis_cache_root_block_device         = var.redis_cache_root_block_device
  redis_cache_ebs_block_devices         = var.redis_cache_ebs_block_devices
  redis_config                          = var.redis_config
  redis_cloudwatch_log_group            = var.redis_cloudwatch_log_group
  redis_enable_monitoring               = var.redis_enable_monitoring
  redis_log_retention_days              = var.redis_log_retention_days

  # =============================================================================
  # SMS SERVICE CONFIGURATION
  # =============================================================================
  
  sms_service_name                      = var.sms_service_name
  sms_service_ami_id                    = var.sms_service_ami_id
  sms_service_instance_type             = var.sms_service_instance_type
  sms_service_key_name                  = var.sms_service_key_name
  sms_service_security_group_ids        = var.sms_service_security_group_ids
  sms_service_subnet_id                 = var.sms_service_subnet_id
  sms_service_availability_zone         = var.sms_service_availability_zone
  sms_service_private_ip_address        = var.sms_service_private_ip_address
  sms_service_monitoring_enabled        = var.sms_service_monitoring_enabled
  sms_service_ebs_optimized             = var.sms_service_ebs_optimized
  sms_service_source_dest_check         = var.sms_service_source_dest_check
  sms_service_iam_instance_profile_name = var.sms_service_iam_instance_profile_name
  sms_service_root_block_device         = var.sms_service_root_block_device
  sms_service_ebs_block_devices         = var.sms_service_ebs_block_devices
  sms_service_additional_ebs_volumes    = var.sms_service_additional_ebs_volumes
  sms_config                            = var.sms_config
  sms_cloudwatch_log_group              = var.sms_cloudwatch_log_group
  sms_enable_monitoring                 = var.sms_enable_monitoring
  sms_log_retention_days                = var.sms_log_retention_days

  # =============================================================================
  # DAM GATEWAY AZ1 CONFIGURATION
  # =============================================================================
  
  dam_gateway_az1_name                      = var.dam_gateway_az1_name
  dam_gateway_az1_ami_id                    = var.dam_gateway_az1_ami_id
  dam_gateway_az1_instance_type             = var.dam_gateway_az1_instance_type
  dam_gateway_az1_key_name                  = var.dam_gateway_az1_key_name
  dam_gateway_az1_security_group_ids        = var.dam_gateway_az1_security_group_ids
  dam_gateway_az1_subnet_id                 = var.dam_gateway_az1_subnet_id
  dam_gateway_az1_availability_zone         = var.dam_gateway_az1_availability_zone
  dam_gateway_az1_private_ip_address        = var.dam_gateway_az1_private_ip_address
  dam_gateway_az1_monitoring_enabled        = var.dam_gateway_az1_monitoring_enabled
  dam_gateway_az1_ebs_optimized             = var.dam_gateway_az1_ebs_optimized
  dam_gateway_az1_source_dest_check         = var.dam_gateway_az1_source_dest_check
  dam_gateway_az1_iam_instance_profile_name = var.dam_gateway_az1_iam_instance_profile_name
  dam_gateway_az1_root_block_device         = var.dam_gateway_az1_root_block_device
  dam_gateway_az1_ebs_block_devices         = var.dam_gateway_az1_ebs_block_devices
  dam_gateway_az1_config                    = var.dam_gateway_az1_config
  dam_gateway_az1_cloudwatch_log_group      = var.dam_gateway_az1_cloudwatch_log_group
  dam_gateway_az1_enable_monitoring         = var.dam_gateway_az1_enable_monitoring
  dam_gateway_az1_log_retention_days        = var.dam_gateway_az1_log_retention_days

  # =============================================================================
  # DAM GATEWAY AZ2 CONFIGURATION
  # =============================================================================
  
  dam_gateway_az2_name                      = var.dam_gateway_az2_name
  dam_gateway_az2_ami_id                    = var.dam_gateway_az2_ami_id
  dam_gateway_az2_instance_type             = var.dam_gateway_az2_instance_type
  dam_gateway_az2_key_name                  = var.dam_gateway_az2_key_name
  dam_gateway_az2_security_group_ids        = var.dam_gateway_az2_security_group_ids
  dam_gateway_az2_subnet_id                 = var.dam_gateway_az2_subnet_id
  dam_gateway_az2_availability_zone         = var.dam_gateway_az2_availability_zone
  dam_gateway_az2_private_ip_address        = var.dam_gateway_az2_private_ip_address
  dam_gateway_az2_monitoring_enabled        = var.dam_gateway_az2_monitoring_enabled
  dam_gateway_az2_ebs_optimized             = var.dam_gateway_az2_ebs_optimized
  dam_gateway_az2_source_dest_check         = var.dam_gateway_az2_source_dest_check
  dam_gateway_az2_iam_instance_profile_name = var.dam_gateway_az2_iam_instance_profile_name
  dam_gateway_az2_root_block_device         = var.dam_gateway_az2_root_block_device
  dam_gateway_az2_ebs_block_devices         = var.dam_gateway_az2_ebs_block_devices
  dam_gateway_az2_config                    = var.dam_gateway_az2_config
  dam_gateway_az2_cloudwatch_log_group      = var.dam_gateway_az2_cloudwatch_log_group
  dam_gateway_az2_enable_monitoring         = var.dam_gateway_az2_enable_monitoring
  dam_gateway_az2_log_retention_days        = var.dam_gateway_az2_log_retention_days
}
