terraform {
  required_version = "~> 1.11.0"

  backend "s3" {
    bucket       = "infovault-s3-native-lock-setup-20250627"
    key          = "bootstrap/terraform.tfstate"
    region       = "ap-southeast-1"
    profile      = ""
    use_lockfile = true
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.0"
    }
  }
}
